# This file is auto generated by SAM CLI build command

[function_build_definitions.3acb220b-9060-47d6-8984-ccb3ac1b7ec8]
packagetype = "Image"
functions = ["BotmaniApi"]

[function_build_definitions.3acb220b-9060-47d6-8984-ccb3ac1b7ec8.metadata]
Dockerfile = "Dockerfile.lambda"
DockerContext = "/Users/<USER>/Product/Botmani/botmani-backend"
DockerTag = "python3.11-v1"

[function_build_definitions.3acb220b-9060-47d6-8984-ccb3ac1b7ec8.metadata.DockerBuildArgs]
STAGE = "dev"

[layer_build_definitions]
