#!/bin/bash

# SAM Deployment Script with Environment Variables
# Usage: ./deploy.sh [dev|staging|prod]

set -e

STAGE=${1:-dev}
VALID_STAGES=("dev" "staging" "prod")

# Check if stage is valid
if [[ ! " ${VALID_STAGES[@]} " =~ " ${STAGE} " ]]; then
    echo "Error: Invalid stage '${STAGE}'"
    echo "Valid stages are: ${VALID_STAGES[*]}"
    exit 1
fi

echo "🚀 Deploying Botmani Backend to ${STAGE} environment..."

# Check if parameter file exists
PARAM_FILE="env.${STAGE}.json"
if [[ ! -f "${PARAM_FILE}" ]]; then
    echo "Error: Parameter file '${PARAM_FILE}' not found!"
    echo "Please create the parameter file with all required environment variables."
    exit 1
fi

echo "📋 Using parameter file: ${PARAM_FILE}"

# Build the application
echo "🔨 Building SAM application..."
sam build --cached --parallel

# Deploy with the specified stage
echo "🚀 Deploying to ${STAGE}..."
sam deploy --config-env ${STAGE} --no-confirm-changeset

# Get the API endpoint
API_URL=$(aws cloudformation describe-stacks --stack-name "botmani-backend-${STAGE}" --query "Stacks[0].Outputs[?OutputKey=='ApiUrl'].OutputValue" --output text 2>/dev/null || echo "")

if [[ -n "${API_URL}" ]]; then
    echo "✅ Deployment successful!"
    echo "🌐 API URL: ${API_URL}"
    echo "🏥 Health Check: ${API_URL}/health"
else
    echo "⚠️  Deployment completed but couldn't retrieve API URL"
fi

echo "📝 Deployment logs and details:"
echo "   Stack name: botmani-backend-${STAGE}"
echo "   Region: us-east-1"
echo "   Stage: ${STAGE}"