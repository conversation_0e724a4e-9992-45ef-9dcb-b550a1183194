classDiagram
    %% Core Entities
    class AgentSystem {
        +List~Provider~ providers
        +List~ApiKey~ api_keys
        +List~Model~ models
        +List~Tool~ tools
        +List~Agent~ agents
        +get_provider_by_id(provider_id: str) Provider
        +get_model_by_id(model_id: str) Model
        +get_tool_by_id(tool_id: str) Tool
        +get_agent_by_id(agent_id: str) Agent
        +get_active_agents() List~Agent~
    }

    class Provider {
        +str id
        +str name
        +ProviderType type
        +str base_url
        +str description
        +bool is_active
        +Dict rate_limits
        +List supported_model_types
        +Dict extra_headers
        +Dict request_params
        +datetime created_at
        +datetime updated_at
    }

    class ApiKey {
        +str id
        +str name
        +str provider_id
        +SecretStr key_value
        +bool is_active
        +int usage_limit
        +int current_usage
        +datetime expires_at
        +List scopes
        +datetime created_at
        +datetime last_used_at
        +validate_usage_against_limit()
    }

    class Model {
        +str id
        +str name
        +str provider_id
        +ModelType model_type
        +str version
        +int max_tokens
        +int max_completion_tokens
        +int context_window
        +float input_cost_per_token
        +float output_cost_per_token
        +bool supports_streaming
        +bool supports_function_calling
        +bool supports_structured_outputs
        +bool supports_json_schema
        +float temperature
        +float top_p
        +float frequency_penalty
        +float presence_penalty
        +str reasoning_effort
        +List capabilities
        +Dict parameters
        +bool is_active
        +datetime created_at
        +datetime updated_at
    }

    class Tool {
        +str id
        +str name
        +ToolType type
        +str description
        +str version
        +str endpoint
        +bool authentication_required
        +List parameters
        +str return_type
        +int timeout
        +int rate_limit
        +Dict function_schema
        +bool sanitize_args
        +bool show_arguments
        +str agent_id
        +List tags
        +bool is_active
        +datetime created_at
        +datetime updated_at
    }

    class ToolParameter {
        +str name
        +str type
        +str description
        +bool required
        +Any default_value
        +List enum_values
        +str validation_pattern
    }

    class Agent {
        +str id
        +str name
        +str description
        +str system_prompt
        +List instructions
        +str model_id
        +List fallback_model_ids
        +List tool_ids
        +AgentConfig config
        +str session_id
        +str user_id
        +str team_id
        +str app_id
        +str workflow_id
        +AgentStatus status
        +List tags
        +Dict metadata
        +Dict usage_stats
        +bool is_public
        +datetime created_at
        +datetime updated_at
        +datetime last_used_at
        +validate_fallback_models()
    }

    class AgentConfig {
        +float temperature
        +int max_tokens
        +int max_completion_tokens
        +float top_p
        +float frequency_penalty
        +float presence_penalty
        +List stop_sequences
        +str reasoning_effort
        +Union tool_choice
        +int tool_call_limit
        +bool show_tool_calls
        +bool markdown
        +bool add_messages_to_memory
        +bool add_tool_execution_results_to_memory
        +int num_messages_to_add_to_model
        +int timeout
        +int retry_attempts
        +Dict custom_parameters
    }

    %% Enumerations
    class ProviderType {
        <<enumeration>>
        OPENAI
        ANTHROPIC
        GOOGLE
        AZURE
        HUGGING_FACE
        COHERE
        MISTRAL
        OLLAMA
        GROQ
        PERPLEXITY
        CEREBRAS
        LITELLM
    }

    class ModelType {
        <<enumeration>>
        CHAT
        COMPLETION
        EMBEDDING
        IMAGE_GENERATION
        TEXT_TO_SPEECH
        SPEECH_TO_TEXT
        REASONING
    }

    class ToolType {
        <<enumeration>>
        FUNCTION
        API
        DATABASE
        WEB_SCRAPER
        FILE_PROCESSOR
        CALCULATOR
        CODE_EXECUTOR
        APIFY
        BRAVE_SEARCH
        FIRECRAWL
        GITHUB
        GMAIL
        GOOGLE_MAPS
    }

    class AgentStatus {
        <<enumeration>>
        ACTIVE
        INACTIVE
        TRAINING
        MAINTENANCE
        DEPRECATED
        PAUSED
        ERROR
    }

    %% Relationships
    AgentSystem "1" *-- "0..*" Provider : contains
    AgentSystem "1" *-- "0..*" ApiKey : contains
    AgentSystem "1" *-- "0..*" Model : contains
    AgentSystem "1" *-- "0..*" Tool : contains
    AgentSystem "1" *-- "0..*" Agent : contains
    
    Provider "1" --o "0..*" ApiKey : has_keys
    Provider "1" --o "0..*" Model : provides_models
    Provider "1" -- "1" ProviderType : type
    
    Model "1" -- "1" ModelType : model_type
    Model "0..*" --o "1" Provider : belongs_to
    
    Tool "1" -- "1" ToolType : type
    Tool "1" *-- "0..*" ToolParameter : has_parameters
    Tool "0..*" --o "0..1" Agent : belongs_to_agent
    
    Agent "1" -- "1" AgentStatus : status
    Agent "0..*" --o "1" Model : uses_primary_model
    Agent "0..*" --o "0..*" Model : has_fallback_models
    Agent "0..*" --o "0..*" Tool : has_access_to
    Agent "1" *-- "1" AgentConfig : configured_by
    
    ApiKey "0..*" --o "1" Provider : authenticates_with