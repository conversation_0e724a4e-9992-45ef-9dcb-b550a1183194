#!/usr/bin/env python3
"""
SSM Parameter Store Manager for botmani project
Manages environment variables between local .env files and AWS SSM Parameter Store
"""

import os
import sys
import boto3
import argparse
from botocore.exceptions import ClientError, NoCredentialsError
from typing import Dict, Optional
from dotenv import load_dotenv
import re

load_dotenv()

class SSMManager:
    def __init__(self, project: str = "botmani"):
        self.project = project
        self.stage = os.getenv("STAGE")
        self.AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
        self.AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
        self.AWS_REGION = os.getenv("AWS_REGION")
        try:
            if self.AWS_ACCESS_KEY_ID and self.AWS_SECRET_ACCESS_KEY:
                self.ssm_client = boto3.client('ssm',
                    aws_access_key_id=self.AWS_ACCESS_KEY_ID,
                    aws_secret_access_key=self.AWS_SECRET_ACCESS_KEY,
                    region_name=self.AWS_REGION
                )
            else:
                self.ssm_client = boto3.client('ssm')
        except NoCredentialsError:
            print("Error: AWS credentials not found. Please configure your AWS credentials.")
            sys.exit(1)
    
    def get_parameter_path(self, stage: str, key: str) -> str:
        """Generate SSM parameter path"""
        return f"/{self.project}/{stage}/{key}"
    
    def load_env_file(self, stage: str) -> Dict[str, str]:
        """Load environment variables from .env.{stage} file"""
        env_file = f".env.{stage}"
        env_vars = {}
        
        if not os.path.exists(env_file):
            print(f"Error: Environment file {env_file} not found.")
            return env_vars
        
        try:
            with open(env_file, 'r') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    # Skip empty lines and comments
                    if not line or line.startswith('#'):
                        continue
                    
                    # Parse KEY=VALUE format
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        
                        # Remove quotes if present
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        elif value.startswith("'") and value.endswith("'"):
                            value = value[1:-1]
                        
                        env_vars[key] = value
                    else:
                        print(f"Warning: Invalid format in {env_file} at line {line_num}: {line}")
        
        except Exception as e:
            print(f"Error reading {env_file}: {e}")
        
        return env_vars
    
    def put_parameter(self, path: str, value: str, overwrite: bool = True) -> bool:
        """Put a parameter in SSM Parameter Store"""
        try:
            self.ssm_client.put_parameter(
                Name=path,
                Value=value,
                Type='String',
                Overwrite=overwrite
            )
            return True
        except ClientError as e:
            print(f"Error putting parameter {path}: {e}")
            return False
    
    def get_parameter(self, path: str) -> Optional[str]:
        """Get a parameter from SSM Parameter Store"""
        try:
            response = self.ssm_client.get_parameter(
                Name=path,
                WithDecryption=True
            )
            return response['Parameter']['Value']
        except ClientError as e:
            if e.response['Error']['Code'] == 'ParameterNotFound':
                return None
            print(f"Error getting parameter {path}: {e}")
            return None
    
    def get_parameters_by_path(self, path: str) -> Dict[str, str]:
        """Get all parameters under a specific path"""
        parameters = {}
        try:
            paginator = self.ssm_client.get_paginator('get_parameters_by_path')
            for page in paginator.paginate(
                Path=path,
                Recursive=True,
                WithDecryption=True
            ):
                for param in page['Parameters']:
                    # Extract key name from full path
                    key = param['Name'].split('/')[-1]
                    parameters[key] = param['Value']
        except ClientError as e:
            print(f"Error getting parameters by path {path}: {e}")
        
        return parameters
    
    def load_env_to_ssm(self, stage: str) -> None:
        """Load environment variables from .env.{stage} to SSM Parameter Store"""
        print(f"Loading environment variables from .env.{stage} to SSM...")
        
        env_vars = self.load_env_file(stage)
        if not env_vars:
            print("No environment variables found to load.")
            return
        
        success_count = 0
        total_count = len(env_vars)
        
        for key, value in env_vars.items():
            parameter_path = self.get_parameter_path(stage, key)
            
            # Check if parameter already exists
            existing_value = self.get_parameter(parameter_path)
            if existing_value is not None:
                if existing_value == value:
                    print(f"✓ {key}: Already up to date")
                else:
                    if self.put_parameter(parameter_path, value, overwrite=True):
                        print(f"✓ {key}: Updated")
                        success_count += 1
                    else:
                        print(f"✗ {key}: Failed to update")
            else:
                if self.put_parameter(parameter_path, value, overwrite=False):
                    print(f"✓ {key}: Created")
                    success_count += 1
                else:
                    print(f"✗ {key}: Failed to create")
        
        print(f"\nSummary: {success_count}/{total_count} parameters processed successfully.")
    
    def dump_ssm_to_env(self, stage: str) -> None:
        """Dump SSM parameters to .env.{stage} file"""
        print(f"Dumping SSM parameters to .env.{stage}...")
        
        stage_path = f"/{self.project}/{stage}"
        parameters = self.get_parameters_by_path(stage_path)
        
        if not parameters:
            print(f"No parameters found for path: {stage_path}")
            return
        
        env_file = f".env.{stage}"
        
        # Backup existing file if it exists
        if os.path.exists(env_file):
            backup_file = f"{env_file}.backup"
            try:
                os.rename(env_file, backup_file)
                print(f"Backed up existing {env_file} to {backup_file}")
            except Exception as e:
                print(f"Warning: Could not backup {env_file}: {e}")
        
        try:
            with open(env_file, 'w') as f:
                f.write(f"# Environment variables for {stage} stage\n")
                f.write(f"# Generated from SSM Parameter Store: {stage_path}\n")
                f.write(f"# Project: {self.project}\n\n")
                
                # Sort keys for consistent output
                for key in sorted(parameters.keys()):
                    value = parameters[key]
                    # Escape quotes in values
                    if '"' in value or "'" in value or ' ' in value:
                        value = f'"{value.replace('"', '\\"')}"'
                    f.write(f"{key}={value}\n")
            
            print(f"✓ Successfully wrote {len(parameters)} parameters to {env_file}")
            
        except Exception as e:
            print(f"Error writing to {env_file}: {e}")
    
    def list_parameters(self, stage: str) -> None:
        """List all parameters for a given stage"""
        print(f"Listing parameters for stage: {stage}")
        
        stage_path = f"/{self.project}/{stage}"
        parameters = self.get_parameters_by_path(stage_path)
        
        if not parameters:
            print(f"No parameters found for path: {stage_path}")
            return
        
        print(f"\nFound {len(parameters)} parameters:")
        for key in sorted(parameters.keys()):
            value = parameters[key]
            # Mask sensitive values
            if any(sensitive in key.lower() for sensitive in ['password', 'secret', 'key', 'token']):
                display_value = "*" * len(value)
            else:
                display_value = value
            print(f"  {key} = {display_value}")

def main():
    parser = argparse.ArgumentParser(description='Manage SSM Parameter Store keys for botmani project')
    parser.add_argument('stage', help='Stage name (e.g., dev, staging, prod)')
    parser.add_argument('action', choices=['load', 'dump', 'list'], 
                      help='Action to perform: load (.env to SSM), dump (SSM to .env), or list (show parameters)')
    parser.add_argument('--project', default='botmani', help='Project name (default: botmani)')
    
    args = parser.parse_args()
    
    # Validate stage name
    if not re.match(r'^[a-zA-Z0-9_-]+$', args.stage):
        print("Error: Stage name can only contain letters, numbers, hyphens, and underscores.")
        sys.exit(1)
    
    manager = SSMManager(args.project)
    
    try:
        if args.action == 'load':
            manager.load_env_to_ssm(args.stage)
        elif args.action == 'dump':
            manager.dump_ssm_to_env(args.stage)
        elif args.action == 'list':
            manager.list_parameters(args.stage)
            
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()