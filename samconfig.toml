version = 0.1

[default]
[default.global]
[default.global.parameters]
stack_name = "botmani-backend"

[default.build]
[default.build.parameters]
cached = true
parallel = true

[default.deploy]
[default.deploy.parameters]
capabilities = "CAPABILITY_IAM"
confirm_changeset = false
resolve_s3 = true
s3_prefix = "botmani-backend"
region = "us-east-1"
image_repositories = []

[dev]
[dev.global]
[dev.global.parameters]
stack_name = "botmani-backend-dev"

[dev.deploy]
[dev.deploy.parameters]
parameter_overrides = [
  "Stage=dev"
]
parameter_overrides_file = "env.dev.json"

[staging]
[staging.global]
[staging.global.parameters]
stack_name = "botmani-backend-staging"

[staging.deploy]
[staging.deploy.parameters]
parameter_overrides = [
  "Stage=staging"
]
parameter_overrides_file = "env.staging.json"

[prod]
[prod.global]
[prod.global.parameters]
stack_name = "botmani-backend-prod"

[prod.deploy]
[prod.deploy.parameters]
parameter_overrides = [
  "Stage=prod"
]
parameter_overrides_file = "env.prod.json"