# Botmani Backend API

A modern, fast, and secure FastAPI backend service with Firebase authentication and MongoDB storage. This API provides authentication-only services using Firebase for Google and Email authentication.

## 🚀 Features

- **FastAPI Framework**: Modern, fast web framework with automatic OpenAPI documentation
- **Firebase Authentication**: Google and Email authentication via Firebase
- **MongoDB Database**: NoSQL document database with Beanie ODM
- **JWT Token Generation**: Generate JWT tokens for API access after Firebase authentication
- **Automatic User Management**: Automatic user creation and linking from Firebase
- **RESTful API Design**: Clean, consistent API endpoints
- **Data Validation**: Pydantic schemas for request/response validation
- **Security**: Firebase token verification, JWT tokens, CORS
- **Health Checks**: Comprehensive health monitoring endpoints
- **Documentation**: Auto-generated OpenAPI/Swagger documentation
- **Environment Configuration**: Flexible configuration management

## 📋 Requirements

- Python 3.8+
- MongoDB (local or cloud)
- Firebase project with Authentication enabled
- Virtual environment (recommended)

## 🛠️ Installation

### 1. <PERSON>lone the repository
```bash
git clone <repository-url>
cd botmani-backend
```

### 2. Create virtual environment
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 3. Install dependencies
```bash
pip install -r requirements.txt
```

### 4. MongoDB Setup
```bash
# Option 1: Local MongoDB installation
mongod

# Option 2: Using Docker
docker run -d -p 27017:27017 --name mongodb mongo:latest

# Option 3: MongoDB Atlas (cloud)
# Create account at https://www.mongodb.com/atlas
# Get connection string from Atlas dashboard
```

### 5. Firebase Setup
1. Create a Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Enable Authentication with Google and Email/Password providers
3. Generate service account key (Project Settings → Service Accounts)
4. Get Web API key (Project Settings → General → Web API Key)

### 6. Environment Configuration
Copy the `.env` file and update with your settings:
```bash
cp .env .env.local
```

Edit `.env.local` with your configuration:
```env
# MongoDB Configuration
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=botmani_db

# Firebase Configuration
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/your-service-account%40your-project-id.iam.gserviceaccount.com
FIREBASE_WEB_API_KEY=your-web-api-key

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
```

### 7. Run the application
```bash
# Development server with auto-reload
python run.py

# Or using uvicorn directly
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

The API will be available at:
- **API Base URL**: http://localhost:8000
- **Interactive Documentation**: http://localhost:8000/api/v1/docs
- **ReDoc Documentation**: http://localhost:8000/api/v1/redoc
- **OpenAPI JSON**: http://localhost:8000/api/v1/openapi.json

## 📚 API Documentation

### Authentication Endpoints

#### POST /api/v1/auth/firebase
Authenticate with Firebase ID token from Google or Email authentication.

**Request:**
```json
{
  "id_token": "firebase-id-token-from-client"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Firebase authentication successful (google)",
  "data": {
    "token": {
      "access_token": "jwt-token-for-api-access",
      "token_type": "bearer",
      "expires_in": 1800
    },
    "user": {
      "id": "60a7c9b4f3d2a1b2c3d4e5f6",
      "email": "<EMAIL>",
      "username": "user_abc123",
      "full_name": "John Doe",
      "firebase_uid": "firebase-user-uid",
      "auth_provider": "google",
      "is_verified": true,
      "avatar_url": "https://example.com/avatar.jpg"
    },
    "provider": "google",
    "firebase_uid": "firebase-user-uid"
  }
}
```

#### GET /api/v1/auth/firebase/config
Get Firebase configuration for client-side initialization.

**Response:**
```json
{
  "success": true,
  "message": "Firebase configuration retrieved",
  "data": {
    "project_id": "your-project-id",
    "auth_domain": "your-project-id.firebaseapp.com",
    "api_key": "your-web-api-key",
    "providers": ["google", "email"]
  }
}
```

#### GET /api/v1/auth/providers
Get available authentication providers.

#### GET /api/v1/auth/status
Get authentication service status.

### Health Check Endpoints

#### GET /api/v1/health
Basic health check.

#### GET /api/v1/health/detailed
Detailed health information with system status.

#### GET /api/v1/health/database
Database-specific health check (MongoDB connectivity).

## 🔒 Authentication Flow

1. **Client Side**: User signs in with Google or Email/Password using Firebase SDK
2. **Client Side**: Get Firebase ID token from authentication result
3. **Client Side**: Send ID token to backend API (`/api/v1/auth/firebase`)
4. **Backend**: Verify Firebase token with Firebase Admin SDK
5. **Backend**: Create or update user in MongoDB
6. **Backend**: Generate JWT token for API access
7. **Backend**: Return JWT token and user data
8. **Client Side**: Use JWT token for subsequent API calls

### Using the JWT Token
Include the JWT token in subsequent API requests:
```bash
Authorization: Bearer <jwt-token>
```

## 🗄️ Database Schema (MongoDB)

### Users Collection
```javascript
{
  _id: ObjectId,
  email: String (unique, indexed),
  username: String (unique, indexed),
  full_name: String,
  firebase_uid: String (unique, indexed),
  auth_provider: String, // "google", "email"
  is_active: Boolean,
  is_superuser: Boolean,
  is_verified: Boolean,
  bio: String,
  avatar_url: String,
  created_at: Date,
  updated_at: Date,
  last_login: Date
}
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PROJECT_NAME` | Project name | Botmani Backend API |
| `DEBUG` | Debug mode | true |
| `SECRET_KEY` | JWT secret key | (required) |
| `MONGODB_URL` | MongoDB connection string | mongodb://localhost:27017 |
| `MONGODB_DATABASE` | MongoDB database name | botmani_db |
| `FIREBASE_PROJECT_ID` | Firebase project ID | (required) |
| `FIREBASE_WEB_API_KEY` | Firebase web API key | (required) |
| `ACCESS_TOKEN_EXPIRE_MINUTES` | JWT token expiration | 30 |

### MongoDB Configuration

**Local MongoDB**:
```env
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=botmani_db
```

**MongoDB Atlas (Cloud)**:
```env
MONGODB_URL=mongodb+srv://username:<EMAIL>/
MONGODB_DATABASE=botmani_db
```

## 🐳 Docker Deployment

### Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["python", "run.py"]
```

### Docker Compose
```yaml
version: '3.8'
services:
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - MONGODB_URL=mongodb://mongo:27017
      - MONGODB_DATABASE=botmani_db
      - FIREBASE_PROJECT_ID=your-project-id
      - FIREBASE_WEB_API_KEY=your-web-api-key
      # Add other Firebase env vars
    depends_on:
      - mongo
  
  mongo:
    image: mongo:7
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db

volumes:
  mongodb_data:
```

## 🌐 Frontend Integration

### JavaScript/React Example
```javascript
import { initializeApp } from 'firebase/app';
import { getAuth, signInWithPopup, GoogleAuthProvider } from 'firebase/auth';

// Get Firebase config from your backend
const getFirebaseConfig = async () => {
  const response = await fetch('/api/v1/auth/firebase/config');
  const data = await response.json();
  return data.data;
};

// Initialize Firebase
const firebaseConfig = await getFirebaseConfig();
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

// Google Sign-In
const signInWithGoogle = async () => {
  const provider = new GoogleAuthProvider();
  try {
    const result = await signInWithPopup(auth, provider);
    const idToken = await result.user.getIdToken();
    
    // Send token to your backend
    const response = await fetch('/api/v1/auth/firebase', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ id_token: idToken }),
    });
    
    const authData = await response.json();
    // Store JWT token for API calls
    localStorage.setItem('token', authData.data.token.access_token);
  } catch (error) {
    console.error('Authentication error:', error);
  }
};
```

## 🧪 Testing

### Running Tests
```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
pytest tests/

# Test specific endpoint
curl -X GET http://localhost:8000/api/v1/auth/firebase/config
```

## 📝 API Response Format

All API responses follow a consistent format:

**Success Response:**
```json
{
  "success": true,
  "message": "Operation successful",
  "data": {
    // Response data
  },
  "meta": {
    "timestamp": "2025-01-07T20:50:44Z",
    "version": "1.0.0"
  }
}
```

**Error Response:**
```json
{
  "success": false,
  "error": {
    "code": "AUTHENTICATION_ERROR",
    "message": "Invalid Firebase token",
    "details": []
  },
  "meta": {
    "timestamp": "2025-01-07T20:50:44Z",
    "version": "1.0.0"
  }
}
```

## 📊 Monitoring

### Health Check Endpoints
- `/api/v1/health` - Basic health status
- `/api/v1/health/detailed` - Comprehensive system info
- `/api/v1/health/database` - MongoDB connectivity

### Logging
The application uses structured logging with configurable levels.

## 🔒 Security Considerations

1. **Firebase Token Verification**: All Firebase tokens are verified server-side
2. **JWT Security**: JWT tokens for API access with configurable expiration
3. **CORS Configuration**: Properly configured CORS for frontend domains
4. **Environment Variables**: Sensitive data stored in environment variables
5. **HTTPS**: Always use HTTPS in production

## 📚 Additional Resources

- [Firebase Authentication Guide](docs/firebase_setup.md)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [MongoDB Documentation](https://docs.mongodb.com/)
- [Beanie ODM Documentation](https://beanie-odm.dev/)
- [Firebase Documentation](https://firebase.google.com/docs)

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Built with ❤️ using FastAPI + Firebase + MongoDB**