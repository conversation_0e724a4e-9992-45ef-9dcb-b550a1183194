# Use AWS Lambda Python runtime
FROM public.ecr.aws/lambda/python:3.11

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=${LAMBDA_TASK_ROOT}

# Accept build argument for stage (defaults to dev)
ARG STAGE=dev

# Copy requirements and install dependencies
COPY requirements.txt ${LAMBDA_TASK_ROOT}/
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir mangum

# Copy application code
COPY app/ ${LAMBDA_TASK_ROOT}/app/
COPY ssm.py ${LAMBDA_TASK_ROOT}/

# Copy the stage-specific .env file as .env
COPY .env.${STAGE} ${LAMBDA_TASK_ROOT}/.env

# Create the lambda handler file
COPY app/lambda_handler.py ${LAMBDA_TASK_ROOT}/app/

# Set the CMD to your handler
<PERSON><PERSON> ["app.lambda_handler.handler"] 