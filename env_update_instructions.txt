IMMEDIATE FIX FOR FIREBASE PROJECT <PERSON><PERSON>ATCH
=====================================================

Your frontend is using Firebase project: 7ud8auqilnueu6f3ofgjvok4o6
Your backend expects Firebase project: botmani-ai

SOLUTION: Update your .env file with these values:

# Replace your current Firebase configuration with:
FIREBASE_PROJECT_ID=7ud8auqilnueu6f3ofgjvok4o6

# You'll also need to get the service account credentials for project 7ud8auqilnueu6f3ofgjvok4o6:
# FIREBASE_CLIENT_EMAIL=<EMAIL>
# FIREBASE_CLIENT_ID=your-client-id-for-project-7ud8auqilnueu6f3ofgjvok4o6
# FIREBASE_PRIVATE_KEY_BASE64=your-base64-encoded-private-key-for-project-7ud8auqilnueu6f3ofgjvok4o6

STEPS:
1. Go to Firebase Console
2. Select project 7ud8auqilnueu6f3ofgjvok4o6
3. Go to Project Settings > Service Accounts
4. Generate a new private key
5. Update your .env file with the new credentials
6. Restart your server

This will immediately resolve the authentication issue.