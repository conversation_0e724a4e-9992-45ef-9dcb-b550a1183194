graph TB
    %% Core Entity Relationships
    AS[AgentSystem] --> P[Provider]
    AS --> AK[ApiKey]
    AS --> M[Model]
    AS --> T[Tool]
    AS --> A[Agent]
    
    %% Provider relationships
    P --> |supports| MT[ModelType]
    P --> |configured with| RateLimit[Rate Limits]
    P --> |has extra headers| Headers[Extra Headers]
    
    %% API Key relationships
    AK --> |belongs to| P
    AK --> |has scopes| Scopes[Scopes]
    AK --> |usage tracking| Usage[Usage Tracking]
    
    %% Model relationships
    M --> |belongs to| P
    M --> |implements| MT
    M --> |has capabilities| Cap[Capabilities]
    M --> |cost tracking| Cost[Token Costs]
    
    %% Tool relationships
    T --> |implements| TT[ToolType]
    T --> |has parameters| TP[ToolParameter]
    T --> |has schema| FS[Function Schema]
    T --> |belongs to agent| A
    
    %% Agent relationships
    A --> |uses primary| M
    A --> |uses fallback| M
    A --> |configured with| AC[AgentConfig]
    A --> |has access to| T
    A --> |has status| AgS[AgentStatus]
    A --> |belongs to| U[User/Team/App]
    
    %% Agent Configuration details
    AC --> |model params| MP[Model Parameters]
    AC --> |tool settings| TS[Tool Settings]
    AC --> |memory config| MC[Memory Config]
    AC --> |timing config| TC[Timing Config]
    
    %% Provider Types
    subgraph "Provider Types"
        PT1[OpenAI]
        PT2[Anthropic]
        PT3[Google]
        PT4[Azure]
        PT5[Hugging Face]
        PT6[Cohere]
        PT7[Mistral]
        PT8[Ollama]
        PT9[Groq]
        PT10[Perplexity]
        PT11[Cerebras]
        PT12[LiteLLM]
    end
    
    %% Model Types
    subgraph "Model Types"
        MT1[Chat]
        MT2[Completion]
        MT3[Embedding]
        MT4[Image Generation]
        MT5[Text to Speech]
        MT6[Speech to Text]
        MT7[Reasoning]
    end
    
    %% Tool Types
    subgraph "Tool Types"
        TT1[Function]
        TT2[API]
        TT3[Database]
        TT4[Web Scraper]
        TT5[File Processor]
        TT6[Calculator]
        TT7[Code Executor]
        TT8[Apify]
        TT9[Brave Search]
        TT10[Firecrawl]
        TT11[GitHub]
        TT12[Gmail]
        TT13[Google Maps]
    end
    
    %% Agent Status Types
    subgraph "Agent Status"
        AgS1[Active]
        AgS2[Inactive]
        AgS3[Training]
        AgS4[Maintenance]
        AgS5[Deprecated]
        AgS6[Paused]
        AgS7[Error]
    end
    
    %% Model Parameters Detail
    subgraph "Model Parameters"
        MP1[Temperature]
        MP2[Max Tokens]
        MP3[Top P]
        MP4[Frequency Penalty]
        MP5[Presence Penalty]
        MP6[Context Window]
        MP7[Streaming Support]
        MP8[Function Calling]
        MP9[Structured Outputs]
        MP10[JSON Schema]
    end
    
    %% Tool Configuration Detail
    subgraph "Tool Configuration"
        TS1[Tool Choice Strategy]
        TS2[Tool Call Limit]
        TS3[Show Tool Calls]
        TS4[Sanitize Args]
        TS5[Show Arguments]
        TS6[Authentication Required]
        TS7[Rate Limiting]
        TS8[Timeout Settings]
    end
    
    %% Memory and Session Management
    subgraph "Memory & Session"
        MC1[Add Messages to Memory]
        MC2[Add Tool Results to Memory]
        MC3[Number of Messages to Model]
        MC4[Session ID]
        MC5[Message History]
    end
    
    %% Usage and Monitoring
    subgraph "Usage & Monitoring"
        UM1[Usage Statistics]
        UM2[Token Tracking]
        UM3[Cost Calculation]
        UM4[Performance Metrics]
        UM5[Error Tracking]
    end
    
    %% Connecting detailed components
    AC --> MP
    AC --> TS
    AC --> MC
    A --> UM
    M --> MP
    T --> TS
    
    %% Styling
    classDef primary fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef secondary fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef config fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef types fill:#fff3e0,stroke:#e65100,stroke-width:2px
    
    class AS,A,M,T,P primary
    class AC,AK,TP,FS secondary
    class MP,TS,MC,TC config
    class MT,TT,AgS,PT1,PT2,PT3,PT4,PT5,PT6,PT7,PT8,PT9,PT10,PT11,PT12 types