services:
  # FastAPI Application
  botmani-backend:
    build: .
    container_name: botmani-backend
    ports:
      - "8000:8000"
    environment:
      # Environment variables that come from .env (not SSM)
      - STAGE=${STAGE:-dev}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION:-us-east-1}
    env_file:
      - .env
      # You can add multiple env files for different environments
      # - dev.env
      # - prod.env
    volumes:
      # Mount code for live development (changes reflected immediately)
      - ./app:/app/app:ro
      - ./requirements.txt:/app/requirements.txt:ro
      # Mount uploads directory for persistent file storage
      - uploads:/app/uploads
    networks:
      - botmani-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # Conditional reload: only enable for local development
    command: >
      sh -c "
      if [ \"$$STAGE\" = \"local\" ]; then
        python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
      else
        python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
      fi
      "

# Named volumes for persistent data
volumes:
  uploads:
    driver: local

# Custom network
networks:
  botmani-network:
    driver: bridge 