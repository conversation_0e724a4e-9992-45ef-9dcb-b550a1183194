# SAM Setup Guide for Botmani Backend

This guide will help you set up and run the Botmani FastAPI application using AWS SAM for Lambda container deployment.

## 📋 Prerequisites

- AWS CLI configured with proper credentials
- AWS SAM CLI installed
- Docker installed and running
- Docker Compose installed
- Python 3.11+

## 🚀 Quick Start

### 1. Configure Environment Variables
Update `env.json` with your actual configuration:
```json
{
  "BotmaniApi": {
    "STAGE": "dev",
    "GOOGLE_CLIENT_ID": "your-actual-google-client-id",
    "GOOGLE_CLIENT_SECRET": "your-actual-google-client-secret",
    "GOOGLE_REDIRECT_URI": "http://localhost:3000/auth/google/callback"
  }
}
```

### 2. Build the Application
```bash
./sam_commands.sh build
```

### 3. Run Locally
```bash
./sam_commands.sh run
```

The API will be available at `http://localhost:3000`

### 4. Test and Debug
```bash
./sam_commands.sh debug
```

## 🔧 Troubleshooting

### Issue: Google OAuth failing with 500 error

**Symptoms:**
- Error: "Failed to create/update Google user: "
- OAuth flow works normally but fails in SAM local

**Common Causes:**

1. **MongoDB Connection Issues**
   - Test: `curl http://localhost:3000/api/v1/debug/mongodb`
   - Solution: Verify MongoDB connection URI is correctly set in SSM Parameter Store

2. **Missing Configuration**
   - Test: `curl http://localhost:3000/api/v1/debug/config`
   - Solution: Update `env.json` with actual Google OAuth credentials

3. **AWS SSM Access Issues**
   - In SAM local, SSM Parameter Store may not be accessible
   - Solution: Use environment variables in `env.json` as fallback

### Debug Endpoints

- **Health**: `http://localhost:3000/api/v1/health`
- **Configuration**: `http://localhost:3000/api/v1/debug/config`
- **MongoDB Status**: `http://localhost:3000/api/v1/debug/mongodb`
- **Environment Info**: `http://localhost:3000/api/v1/debug/environment`

### Common Commands

```bash
# Build application
./sam_commands.sh build

# Run locally
./sam_commands.sh run

# Test debug endpoints
./sam_commands.sh debug

# Clean build artifacts
./sam_commands.sh cleanup

# Deploy to AWS
./sam_commands.sh deploy dev
```

## 🏗️ Architecture

### Local Development Setup
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │───▶│   SAM Local     │───▶│   Cloud MongoDB │
│ (localhost:3000)│    │ (localhost:3000)│    │   (MongoDB Atlas)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Configuration Flow
1. Environment variables from `env.json`
2. Fallback to AWS SSM Parameter Store (if accessible)
3. Default values from `app/core/config.py`

## 📝 Configuration

### Required Environment Variables (in env.json)
- `STAGE`: Environment stage (dev/staging/prod)
- `GOOGLE_CLIENT_ID`: Google OAuth client ID
- `GOOGLE_CLIENT_SECRET`: Google OAuth client secret
- `GOOGLE_REDIRECT_URI`: OAuth redirect URI

Note: MongoDB connection URI will be loaded from SSM Parameter Store based on STAGE.

### Optional AWS SSM Parameters
- Format: `/botmani/{stage}/{parameter_name}`
- Examples:
  - `/botmani/dev/MONGODB_URL`
  - `/botmani/dev/GOOGLE_CLIENT_ID`

## 🔐 Security Notes

- Never commit real credentials to version control
- Use AWS SSM Parameter Store for production credentials
- The `env.json` file contains example values for local development
- Update Google OAuth redirect URIs in Google Cloud Console

## 📖 Additional Resources

- [AWS SAM Documentation](https://docs.aws.amazon.com/serverless-application-model/)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Google OAuth Setup Guide](./docs/google_oauth_setup.md)
- [Firebase Setup Guide](./docs/firebase_setup.md) 