# Git
.git
.gitignore
.gitattributes

# CI/CD
.github
.gitlab-ci.yml

# Documentation
README.md
README_DEPLOYMENT.md
docs/
*.md

# Environment files
.env
.env.common
.env.local
!.env.dev
!.env.prod
!.env.staging
!.env.example

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.venv/

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Node modules (if any)
node_modules/
npm-debug.log

# Temporary files
*.tmp
*.temp

# Build artifacts
dist/
build/
*.egg-info/

# Docker
Dockerfile*
docker-compose*.yml
.docker/

# Local uploads (should not be in container)
uploads/

# AWS/Cloud
.aws/
.boto

# Database
*.db
*.sqlite
*.sqlite3

# Jupyter
.ipynb_checkpoints/

# MyPy
.mypy_cache/
.dmypy.json
dmypy.json 