#!/bin/bash

# SAM Helper Commands for Botmani Backend

echo "🚀 Botmani Backend SAM Commands"
echo "=============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
# Environment files now bundled in container as .env.{stage}

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_command() {
    echo -e "${BLUE}[CMD]${NC} $1"
}

# Function to display usage
usage() {
    echo "Usage: $0 [build|run|deploy|deploy-env|deploy-guided|logs|cleanup|debug|validate-env] [stage]"
    echo ""
    echo "Commands:"
    echo "  build        - Build the SAM application"
    echo "  run          - Run locally with sam local start-api"
    echo "  deploy       - Deploy to AWS using samconfig.toml with .env.{stage} bundled in container"
    echo "  deploy-env   - Deploy to AWS with .env.{stage} bundled in container (alternative method)"
    echo "  deploy-guided - Deploy with SAM guided deployment (interactive)"
    echo "  logs         - Tail logs from deployed function"
    echo "  cleanup      - Clean SAM build artifacts"
    echo "  debug        - Test debug endpoints"
    echo "  validate-env - Validate .env.{stage} file exists and has basic variables"
    echo ""
    echo "Examples:"
    echo "  $0 build"
    echo "  $0 run"
    echo "  $0 debug"
    echo "  $0 validate-env dev"
    echo "  $0 deploy dev"
    echo "  $0 deploy-env dev"
    echo "  $0 deploy-guided dev"
    echo "  $0 logs dev"
    exit 1
}

# Function to check if variable is set and not empty
check_required_var() {
    local var_name=$1
    local var_value=${!var_name}
    
    if [[ -z "$var_value" ]]; then
        print_error "Required environment variable $var_name is not set or is empty"
        return 1
    fi
}

# Function to validate .env.{stage} file exists and has basic variables
validate_env() {
    local stage=${1:-dev}
    local env_file=".env.${stage}"
    
    print_status "Validating environment file: $env_file"
    
    # Check if .env.{stage} file exists
    if [[ ! -f "$env_file" ]]; then
        print_error "$env_file file not found!"
        print_status "Please create a $env_file file with required environment variables."
        return 1
    fi

    # Check if file has STAGE variable
    if ! grep -q "^STAGE=" "$env_file"; then
        print_error "STAGE variable not found in $env_file"
        return 1
    fi

    print_status "✅ Environment file $env_file exists and looks valid."
    return 0
}



# Build function
build() {
    print_status "🔨 Building SAM application..."
    print_command "sam build --use-container"
    sam build --use-container
    if [ $? -eq 0 ]; then
        print_status "✅ Build completed successfully!"
    else
        print_error "❌ Build failed!"
        exit 1
    fi
}

# Run locally
run_local() {
    print_status "🏃 Starting local API..."
    echo "API will be available at: http://localhost:8000"
    echo ""
    echo "📋 Useful endpoints for debugging:"
    echo "   - Health: http://localhost:8000/api/v1/health"
    echo "   - Config: http://localhost:8000/api/v1/debug/config"
    echo "   - MongoDB: http://localhost:8000/api/v1/debug/mongodb"
    echo "   - Environment: http://localhost:8000/api/v1/debug/environment"
    echo ""
    
    print_command "sam local start-api --port 8000"
    sam local start-api --port 8000
}

# Debug endpoints
debug() {
    print_status "🔍 Testing debug endpoints..."
    
    # Check if API is running
    if ! curl -s http://localhost:8000/api/v1/health > /dev/null; then
        print_error "❌ API is not running. Start it first with: $0 run"
        exit 1
    fi
    
    echo ""
    print_status "🏥 Health Check:"
    curl -s http://localhost:8000/api/v1/health | python3 -m json.tool || echo "Response is not valid JSON"
    
    echo ""
    print_status "⚙️  Configuration:"
    curl -s http://localhost:8000/api/v1/debug/config | python3 -m json.tool || echo "Response is not valid JSON"
    
    echo ""
    print_status "🗄️  MongoDB Status:"
    curl -s http://localhost:8000/api/v1/debug/mongodb | python3 -m json.tool || echo "Response is not valid JSON"
    
    echo ""
    print_status "🌍 Environment:"
    curl -s http://localhost:8000/api/v1/debug/environment | python3 -m json.tool || echo "Response is not valid JSON"
}

# Simple deploy function using samconfig.toml (no parameter overrides)
deploy_simple() {
    local stage=$1
    
    if [[ -z "$stage" ]]; then
        print_error "❌ Stage is required for deployment"
        echo "Available stages: dev, staging, prod"
        exit 1
    fi

    if [[ ! "$stage" =~ ^(dev|staging|prod)$ ]]; then
        print_error "❌ Invalid stage: $stage"
        echo "Available stages: dev, staging, prod"
        exit 1
    fi

    # Check if samconfig.toml exists
    if [[ ! -f "samconfig.toml" ]]; then
        print_warning "samconfig.toml not found. This is recommended for deployment configuration."
        print_status "You can create one using the samconfig.toml template provided."
    fi

    # Check if .env.{stage} file exists
    if [[ ! -f ".env.${stage}" ]]; then
        print_error "❌ Environment file .env.${stage} not found!"
        exit 1
    fi

    print_status "🚀 Deploying to $stage environment using samconfig.toml..."
    print_status "Using environment file: .env.${stage}"
    
    print_command "sam deploy --config-env $stage --parameter-overrides Stage=$stage --resolve-image-repos --resolve-s3 --capabilities CAPABILITY_IAM"
    
    sam deploy --config-env $stage --parameter-overrides "Stage=$stage" --resolve-image-repos --resolve-s3 --capabilities CAPABILITY_IAM

    if [[ $? -eq 0 ]]; then
        print_status "✅ Deployment completed successfully!"
        
        # Try to get the API URL from stack outputs
        STACK_NAME="botmani-backend-${stage}"
        API_URL=$(aws cloudformation describe-stacks \
            --stack-name "$STACK_NAME" \
            --query 'Stacks[0].Outputs[?OutputKey==`ApiUrl`].OutputValue' \
            --output text 2>/dev/null)
        
        if [[ -n "$API_URL" ]]; then
            print_status "🌐 API URL: $API_URL"
        fi
        
        print_status "📦 Stack: $STACK_NAME"
    else
        print_error "❌ Deployment failed!"
        exit 1
    fi
}

# Deploy function with environment variables (simplified - now uses .env files in container)
deploy_with_env() {
    local stage=$1
    
    if [[ -z "$stage" ]]; then
        print_error "❌ Stage is required for deployment"
        echo "Available stages: dev, staging, prod"
        exit 1
    fi

    if [[ ! "$stage" =~ ^(dev|staging|prod)$ ]]; then
        print_error "❌ Invalid stage: $stage"
        echo "Available stages: dev, staging, prod"
        exit 1
    fi

    # Check if .env.{stage} file exists
    if [[ ! -f ".env.${stage}" ]]; then
        print_error "❌ Environment file .env.${stage} not found!"
        exit 1
    fi

    # Build the final stack name with stage
    STACK_NAME="botmani-backend-${stage}"

    print_status "🚀 Deploying to $stage environment..."
    print_status "Stack name: $STACK_NAME"
    print_status "Using environment file: .env.${stage}"
    
    print_command "sam deploy --stack-name $STACK_NAME --parameter-overrides Stage=$stage --capabilities CAPABILITY_IAM --resolve-image-repos --resolve-s3 --no-confirm-changeset --no-fail-on-empty-changeset"
    
    sam deploy \
        --stack-name "$STACK_NAME" \
        --parameter-overrides "Stage=$stage" \
        --capabilities CAPABILITY_IAM \
        --resolve-image-repos \
        --resolve-s3 \
        --no-confirm-changeset \
        --no-fail-on-empty-changeset

    if [[ $? -eq 0 ]]; then
        print_status "✅ Deployment completed successfully!"
        
        # Get the API URL from stack outputs
        API_URL=$(aws cloudformation describe-stacks \
            --stack-name "$STACK_NAME" \
            --query 'Stacks[0].Outputs[?OutputKey==`ApiUrl`].OutputValue' \
            --output text 2>/dev/null)
        
        if [[ -n "$API_URL" ]]; then
            print_status "🌐 API URL: $API_URL"
        fi
        
        print_status "📦 Stack: $STACK_NAME"
    else
        print_error "❌ Deployment failed!"
        exit 1
    fi
}

# Deploy with guided setup (original SAM behavior)
deploy_guided() {
    local stage=$1
    if [[ -z "$stage" ]]; then
        print_error "❌ Stage is required for guided deployment"
        echo "Available stages: dev, staging, prod"
        exit 1
    fi

    if [[ ! "$stage" =~ ^(dev|staging|prod)$ ]]; then
        print_error "❌ Invalid stage: $stage"
        echo "Available stages: dev, staging, prod"
        exit 1
    fi

    print_status "🚀 Deploying to $stage environment with guided setup..."
    print_command "sam deploy --config-env $stage --guided"
    sam deploy --config-env $stage --guided
}

# Tail logs
tail_logs() {
    local stage=${1:-dev}
    if [[ -z "$stage" ]]; then
        stage="dev"
    fi
    
    print_status "📋 Tailing logs for $stage environment..."
    print_command "sam logs --name \"botmani-backend-$stage\" --stack-name \"botmani-backend-$stage\" --tail"
    sam logs --name "botmani-backend-$stage" --stack-name "botmani-backend-$stage" --tail
}

# Cleanup
cleanup() {
    print_status "🧹 Cleaning up SAM build artifacts..."
    print_command "rm -rf .aws-sam"
    rm -rf .aws-sam
    print_status "✅ Cleanup completed!"
}

# Main script logic
case "$1" in
    build)
        build
        ;;
    run)
        run_local
        ;;
    debug)
        debug
        ;;
    deploy)
        deploy_simple $2
        ;;
    deploy-env)
        deploy_with_env $2
        ;;
    deploy-guided)
        deploy_guided $2
        ;;
    logs)
        tail_logs $2
        ;;
    cleanup)
        cleanup
        ;;
    validate-env)
        stage=${2:-dev}
        if validate_env "$stage"; then
            print_status "✅ Environment validation passed!"
        else
            exit 1
        fi
        ;;
    *)
        usage
        ;;
esac