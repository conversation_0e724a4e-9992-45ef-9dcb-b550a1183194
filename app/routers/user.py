"""
User management endpoints with Bear<PERSON> JWT authentication.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional
from datetime import datetime

from app.dependencies.auth import get_current_user, get_current_active_user, get_current_verified_user
from app.models.user import User
from app.schemas.base import BaseResponse
from app.schemas.user import UserResponse, UserUpdate
from app.core.config import settings

router = APIRouter()


@router.get("/me", response_model=BaseResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """
    Get current authenticated user information.
    
    Requires: Bearer JWT token
    
    Returns:
        BaseResponse: Current user information
    """
    return BaseResponse(
        success=True,
        message="Current user information retrieved successfully",
        data={
            "user": current_user.to_dict()
        },
        meta={
            "timestamp": settings.get_current_timestamp(),
            "version": settings.VERSION
        }
    )


@router.put("/me", response_model=BaseResponse)
async def update_current_user(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_active_user)
):
    """
    Update current authenticated user information.
    
    Requires: Bearer JWT token and active user
    
    Args:
        user_update: User update data
        current_user: Current authenticated user
        
    Returns:
        BaseResponse: Updated user information
    """
    try:
        # Update user fields
        update_fields = user_update.dict(exclude_unset=True)
        
        for field, value in update_fields.items():
            if hasattr(current_user, field) and value is not None:
                setattr(current_user, field, value)
        
        # Update timestamp
        current_user.updated_at = datetime.utcnow()
        
        # Save changes
        await current_user.save_changes()
        
        return BaseResponse(
            success=True,
            message="User information updated successfully",
            data={
                "user": current_user.to_dict()
            },
            meta={
                "timestamp": settings.get_current_timestamp(),
                "version": settings.VERSION
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update user: {str(e)}"
        )


@router.get("/profile", response_model=BaseResponse)
async def get_user_profile(
    current_user: User = Depends(get_current_verified_user)
):
    """
    Get detailed user profile information.
    
    Requires: Bearer JWT token, active and verified user
    
    Returns:
        BaseResponse: Detailed user profile
    """
    return BaseResponse(
        success=True,
        message="User profile retrieved successfully",
        data={
            "profile": {
                "id": str(current_user.id),
                "username": current_user.username,
                "email": current_user.email,
                "full_name": current_user.full_name,
                "avatar_url": current_user.avatar_url,
                "auth_provider": current_user.auth_provider,
                "is_verified": current_user.is_verified,
                "is_active": current_user.is_active,
                "created_at": current_user.created_at.isoformat() if current_user.created_at else None,
                "updated_at": current_user.updated_at.isoformat() if current_user.updated_at else None,
                "last_login": current_user.last_login.isoformat() if current_user.last_login else None,
            }
        },
        meta={
            "timestamp": settings.get_current_timestamp(),
            "version": settings.VERSION
        }
    )


@router.delete("/me", response_model=BaseResponse)
async def delete_current_user(
    current_user: User = Depends(get_current_active_user)
):
    """
    Delete current authenticated user account.
    
    Requires: Bearer JWT token and active user
    
    Returns:
        BaseResponse: Confirmation of account deletion
    """
    try:
        # Instead of hard delete, we'll deactivate the user
        current_user.is_active = False
        current_user.updated_at = datetime.utcnow()
        await current_user.save_changes()
        
        return BaseResponse(
            success=True,
            message="User account deactivated successfully",
            data={
                "user_id": str(current_user.id),
                "deactivated_at": current_user.updated_at.isoformat()
            },
            meta={
                "timestamp": settings.get_current_timestamp(),
                "version": settings.VERSION
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to deactivate user: {str(e)}"
        )


@router.get("/settings", response_model=BaseResponse)
async def get_user_settings(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get user settings and preferences.
    
    Requires: Bearer JWT token and active user
    
    Returns:
        BaseResponse: User settings
    """
    return BaseResponse(
        success=True,
        message="User settings retrieved successfully",
        data={
            "settings": {
                "user_id": str(current_user.id),
                "email_notifications": True,  # Default setting
                "privacy_level": "normal",    # Default setting
                "theme": "auto",             # Default setting
                "language": "en",            # Default setting
            }
        },
        meta={
            "timestamp": settings.get_current_timestamp(),
            "version": settings.VERSION
        }
    )


@router.post("/change-password", response_model=BaseResponse)
async def change_password(
    current_user: User = Depends(get_current_verified_user)
):
    """
    Change user password (Firebase users only).
    
    Requires: Bearer JWT token, active and verified user
    
    Note: Password changes are handled through Firebase Auth.
    This endpoint provides information on how to change password.
    
    Returns:
        BaseResponse: Instructions for password change
    """
    if current_user.auth_provider != "email":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password change is only available for email/password users"
        )
    
    return BaseResponse(
        success=True,
        message="Password change instructions",
        data={
            "message": "To change your password, please use the Firebase Auth password reset flow",
            "instructions": [
                "Use the Firebase Auth SDK to send a password reset email",
                "Check your email for the password reset link",
                "Follow the link to set a new password"
            ],
            "auth_provider": current_user.auth_provider
        },
        meta={
            "timestamp": settings.get_current_timestamp(),
            "version": settings.VERSION
        }
    )