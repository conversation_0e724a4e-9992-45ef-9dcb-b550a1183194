"""
Health check endpoints for monitoring service status.
"""

import time
from datetime import datetime
from fastapi import APIRouter, HTTPException

from app.core.config import settings
from app.core.mongodb import mongodb_client, get_database
from app.core.firebase_auth import firebase_auth
from app.schemas.base import BaseResponse, HealthCheck

# Service start time for uptime calculation
service_start_time = time.time()

router = APIRouter()


@router.get("/health", response_model=BaseResponse)
async def health_check():
    """
    Basic health check endpoint.
    
    Returns:
        BaseResponse: Health status information
    """
    try:
        # Test MongoDB connection
        if mongodb_client:
            await mongodb_client.admin.command('ping')
            db_status = "connected"
            db_error = None
        else:
            db_status = "not_initialized"
            db_error = "MongoDB client not initialized"
    except Exception as e:
        db_status = "disconnected"
        db_error = str(e)
    
    # Calculate uptime
    uptime = time.time() - service_start_time
    
    # Determine overall health - service is healthy even without database for demo purposes
    is_healthy = True  # Allow service to be healthy without database
    
    health_data = HealthCheck(
        status="healthy" if is_healthy else "unhealthy",
        timestamp=datetime.utcnow(),
        version=settings.VERSION,
        uptime=uptime,
        database={
            "status": db_status,
            "error": db_error
        }
    )
    
    return BaseResponse(
        success=is_healthy,
        message="Service is healthy" if is_healthy else "Service has issues",
        data=health_data.dict(),
        meta={
            "timestamp": settings.get_current_timestamp(),
            "version": settings.VERSION
        }
    )


@router.get("/health/detailed", response_model=BaseResponse)
async def detailed_health_check():
    """
    Detailed health check endpoint with comprehensive system information.
    
    Returns:
        BaseResponse: Detailed health status information
    """
    health_info = {
        "service": {
            "name": settings.PROJECT_NAME,
            "version": settings.VERSION,
            "uptime": time.time() - service_start_time,
            "timestamp": datetime.utcnow().isoformat() + "Z"
        },
        "database": {
            "status": "unknown",
            "type": "MongoDB",
            "connection_info": {},
            "error": None
        },
        "firebase": {
            "status": "available" if firebase_auth.is_available() else "unavailable",
            "initialized": firebase_auth._app is not None
        },
        "configuration": {
            "environment": "development" if settings.DEBUG else "production",
            "debug_mode": settings.DEBUG,
            "cors_origins": len(settings.BACKEND_CORS_ORIGINS),
            "rate_limit": {
                "requests": settings.RATE_LIMIT_REQUESTS,
                "window": settings.RATE_LIMIT_WINDOW
            }
        }
    }
    
    # Test MongoDB connection and get info
    try:
        if mongodb_client:
            await mongodb_client.admin.command('ping')
            server_info = await mongodb_client.admin.command('serverStatus')
            health_info["database"]["status"] = "connected"
            health_info["database"]["connection_info"] = {
                "server_version": server_info.get("version", "unknown"),
                "database_name": settings.MONGODB_DATABASE,
                "connection_url": settings.MONGODB_URL.split('@')[-1] if '@' in settings.MONGODB_URL else settings.MONGODB_URL
            }
        else:
            health_info["database"]["status"] = "not_available"
            health_info["database"]["error"] = "MongoDB client not initialized"
    except Exception as e:
        health_info["database"]["status"] = "disconnected"
        health_info["database"]["error"] = str(e)
    
    # Determine overall health - service is healthy even without database for demo purposes
    is_healthy = True  # Allow service to be healthy without database
    
    return BaseResponse(
        success=is_healthy,
        message="Detailed health check completed",
        data=health_info,
        meta={
            "timestamp": settings.get_current_timestamp(),
            "version": settings.VERSION
        }
    )


@router.get("/health/database", response_model=BaseResponse)
async def database_health_check():
    """
    Database-specific health check endpoint.
    
    Returns:
        BaseResponse: Database health status
    """
    try:
        if not mongodb_client:
            raise Exception("MongoDB client not initialized")
        
        # Test basic connection
        await mongodb_client.admin.command('ping')
        
        # Get database info
        database = get_database()
        collections = await database.list_collection_names()
        
        # Check if users collection exists
        users_collection_exists = "users" in collections
        
        # Get database stats
        stats = await database.command("dbStats")
        
        database_health = {
            "status": "healthy",
            "connection": "active",
            "database_name": settings.MONGODB_DATABASE,
            "collections": collections,
            "users_collection_exists": users_collection_exists,
            "stats": {
                "collections_count": stats.get("collections", 0),
                "data_size": stats.get("dataSize", 0),
                "storage_size": stats.get("storageSize", 0)
            },
            "connection_info": {
                "url": settings.MONGODB_URL.split('@')[-1] if '@' in settings.MONGODB_URL else settings.MONGODB_URL,
                "database": settings.MONGODB_DATABASE
            },
            "test_query_success": True
        }
        
        return BaseResponse(
            success=True,
            message="Database is healthy",
            data=database_health,
            meta={
                "timestamp": settings.get_current_timestamp(),
                "version": settings.VERSION
            }
        )
        
    except Exception as e:
        database_health = {
            "status": "unhealthy",
            "connection": "failed",
            "error": str(e),
            "test_query_success": False
        }
        
        return BaseResponse(
            success=False,
            message="Database health check failed",
            data=database_health,
            meta={
                "timestamp": settings.get_current_timestamp(),
                "version": settings.VERSION
            }
        )


@router.get("/health/ready", response_model=BaseResponse)
async def readiness_check():
    """
    Readiness check endpoint for container orchestration.
    
    Returns:
        BaseResponse: Readiness status
    """
    try:
        # Test MongoDB connection
        if mongodb_client:
            await mongodb_client.admin.command('ping')
            db_ready = True
            db_status = "ready"
        else:
            db_ready = False
            db_status = "not_initialized"
        
        # Check Firebase availability
        firebase_ready = firebase_auth.is_available()
        
        overall_ready = db_ready and firebase_ready
        
        readiness_status = {
            "ready": overall_ready,
            "database": db_status,
            "firebase": "ready" if firebase_ready else "not_ready",
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
        
        if overall_ready:
            return BaseResponse(
                success=True,
                message="Service is ready",
                data=readiness_status,
                meta={
                    "timestamp": settings.get_current_timestamp(),
                    "version": settings.VERSION
                }
            )
        else:
            raise HTTPException(
                status_code=503,
                detail={
                    "success": False,
                    "message": "Service is not ready",
                    "data": readiness_status,
                    "meta": {
                        "timestamp": settings.get_current_timestamp(),
                        "version": settings.VERSION
                    }
                }
            )
        
    except HTTPException:
        raise
    except Exception as e:
        readiness_status = {
            "ready": False,
            "database": "error",
            "firebase": "unknown",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
        
        raise HTTPException(
            status_code=503,
            detail={
                "success": False,
                "message": "Service is not ready",
                "data": readiness_status,
                "meta": {
                    "timestamp": settings.get_current_timestamp(),
                    "version": settings.VERSION
                }
            }
        )


@router.get("/health/live", response_model=BaseResponse)
async def liveness_check():
    """
    Liveness check endpoint for container orchestration.
    
    Returns:
        BaseResponse: Liveness status
    """
    liveness_status = {
        "alive": True,
        "uptime": time.time() - service_start_time,
        "timestamp": datetime.utcnow().isoformat() + "Z"
    }
    
    return BaseResponse(
        success=True,
        message="Service is alive",
        data=liveness_status,
        meta={
            "timestamp": settings.get_current_timestamp(),
            "version": settings.VERSION
        }
    )