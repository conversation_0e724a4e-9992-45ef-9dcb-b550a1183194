"""
Authentication endpoints for Google OAuth and Firebase authentication.
"""

import logging
from datetime import datetime, timed<PERSON>ta
from typing import Optional
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel
import httpx

logger = logging.getLogger(__name__)

from app.core.security import create_access_token
from app.core.config import settings
from app.core.firebase_auth import firebase_auth, FirebaseUser
from app.models.user import User
from app.schemas.base import BaseResponse

router = APIRouter()


class GoogleTokenRequest(BaseModel):
    """Request model for Google OAuth token exchange."""
    code: str


class FirebaseTokenRequest(BaseModel):
    """Request model for Firebase ID token authentication."""
    id_token: str


class CreateUserRequest(BaseModel):
    """Request model for creating a new user."""
    email: str
    password: Optional[str] = None
    full_name: Optional[str] = None
    avatar_url: Optional[str] = None


@router.post("/google/callback/token", response_model=BaseResponse)
async def google_oauth_token_exchange(request: GoogleTokenRequest):
    """
    Exchange Google authorization code for tokens and create/update user.
    
    Args:
        request: Contains the authorization code from Google OAuth
        
    Returns:
        BaseResponse: Tokens and user data
    """
    try:
        code = request.code
        
        if not code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Authorization code is required"
            )
        
        if not settings.GOOGLE_CLIENT_ID or not settings.GOOGLE_CLIENT_SECRET or not settings.GOOGLE_REDIRECT_URI:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Google OAuth not configured - missing GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, or GOOGLE_REDIRECT_URI"
            )
        
        # Exchange authorization code for tokens
        logger.info(f"Attempting token exchange with redirect_uri: {settings.GOOGLE_REDIRECT_URI}")
        
        async with httpx.AsyncClient() as client:
            token_data_payload = {
                'code': code,
                'client_id': settings.GOOGLE_CLIENT_ID,
                'client_secret': settings.GOOGLE_CLIENT_SECRET,
                'redirect_uri': settings.GOOGLE_REDIRECT_URI,
                'grant_type': 'authorization_code',
            }
            
            logger.info(f"Token exchange payload (without secrets): code=[REDACTED], client_id={settings.GOOGLE_CLIENT_ID[:10]}..., redirect_uri={settings.GOOGLE_REDIRECT_URI}")
            
            token_response = await client.post('https://oauth2.googleapis.com/token', data=token_data_payload)
            
            if token_response.status_code != 200:
                logger.error(f"Token exchange failed with status {token_response.status_code}: {token_response.text}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Failed to exchange authorization code for tokens: {token_response.json().get('error_description', 'Unknown error')}"
                )
            
            token_data = token_response.json()
        
        # Extract token information
        access_token = token_data.get('access_token')
        id_token = token_data.get('id_token')
        refresh_token = token_data.get('refresh_token')
        expires_in = token_data.get('expires_in')
        token_type = token_data.get('token_type', 'Bearer')
        
        if not access_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to obtain access token from Google"
            )
        
        # Get user info from Google
        async with httpx.AsyncClient() as client:
            user_response = await client.get('https://www.googleapis.com/oauth2/v2/userinfo', headers={
                'Authorization': f'Bearer {access_token}',
            })
            
            if user_response.status_code != 200:
                logger.error(f"Failed to get user info: {user_response.text}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to get user information from Google"
                )
            
            google_user_info = user_response.json()
        
        # Create or update user in database
        user = await get_or_create_google_user(google_user_info)
        
        # Update last login
        user.last_login = datetime.utcnow()
        await user.save_changes()
        
        # Create our JWT access token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        jwt_access_token = create_access_token(
            data={"sub": user.username, "user_id": str(user.id)},
            expires_delta=access_token_expires
        )
        
        return BaseResponse(
            success=True,
            message="Google OAuth authentication successful",
            data={
                "access_token": access_token,
                "id_token": id_token,
                "refresh_token": refresh_token,
                "expires_in": expires_in,
                "token_type": token_type,
                "jwt_access_token": jwt_access_token,
                "user": user.to_dict(),
                "provider": "google"
            },
            meta={
                "timestamp": settings.get_current_timestamp(),
                "version": settings.VERSION
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        full_error = traceback.format_exc()
        logger.error(f"OAuth token exchange failed: {str(e)}")
        logger.error(f"Full traceback: {full_error}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"OAuth token exchange failed: {str(e)}"
        )


@router.post("/firebase", response_model=BaseResponse)
async def firebase_authentication(request: FirebaseTokenRequest):
    """
    Authenticate with Firebase ID token and create/update user in MongoDB.
    
    Args:
        request: Contains the Firebase ID token
        
    Returns:
        BaseResponse: JWT token and user data
    """
    try:
        id_token = request.id_token
        
        if not id_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Firebase ID token is required"
            )
        
        # Check if Firebase auth service is available
        if not firebase_auth.is_available():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Firebase authentication service not configured"
            )
        
        # Verify Firebase ID token
        firebase_user = await firebase_auth.verify_firebase_token(id_token)
        
        # Create or update user in MongoDB
        user = await get_or_create_firebase_user(firebase_user)
        
        # Update last login
        user.last_login = datetime.utcnow()
        await user.save_changes()
        
        # Create JWT access token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        jwt_access_token = create_access_token(
            data={"sub": user.username, "user_id": str(user.id)},
            expires_delta=access_token_expires
        )
        
        return BaseResponse(
            success=True,
            message="Firebase authentication successful",
            data={
                "access_token": jwt_access_token,
                "token_type": "bearer",
                "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
                "user": user.to_dict(),
                "provider": firebase_user.provider
            },
            meta={
                "timestamp": settings.get_current_timestamp(),
                "version": settings.VERSION
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Firebase authentication failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Firebase authentication failed: {str(e)}"
        )


@router.post("/firebase/create-user", response_model=BaseResponse)
async def create_firebase_user(request: CreateUserRequest):
    """
    Create a new user in Firebase and MongoDB.
    
    Args:
        request: Contains user creation data
        
    Returns:
        BaseResponse: Created user data and JWT token
    """
    try:
        email = request.email
        password = request.password
        full_name = request.full_name
        avatar_url = request.avatar_url
        
        if not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email is required"
            )
        
        # Check if Firebase auth service is available
        if not firebase_auth.is_available():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Firebase authentication service not configured"
            )
        
        # Check if user already exists in MongoDB
        existing_user = await User.find_by_email(email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this email already exists"
            )
        
        # Create user in Firebase
        firebase_uid = await firebase_auth.create_firebase_user(
            email=email,
            password=password,
            display_name=full_name,
            photo_url=avatar_url,
            email_verified=False,  # User will need to verify email
            provider="email" if password else "google"
        )
        
        # Create user in MongoDB
        username = email.split("@")[0]
        
        # Ensure username is unique
        counter = 1
        original_username = username
        while await User.find_one({"username": username}):
            username = f"{original_username}{counter}"
            counter += 1
        
        new_user = User(
            email=email,
            username=username,
            full_name=full_name,
            avatar_url=avatar_url,
            firebase_uid=firebase_uid,
            auth_provider="email" if password else "google",
            is_verified=False,  # User will need to verify email
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        await new_user.insert()
        
        # Create JWT access token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        jwt_access_token = create_access_token(
            data={"sub": new_user.username, "user_id": str(new_user.id)},
            expires_delta=access_token_expires
        )
        
        logger.info(f"Created new user in Firebase and MongoDB: {email}")
        
        return BaseResponse(
            success=True,
            message="User created successfully in Firebase and MongoDB",
            data={
                "access_token": jwt_access_token,
                "token_type": "bearer",
                "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
                "user": new_user.to_dict(),
                "firebase_uid": firebase_uid
            },
            meta={
                "timestamp": settings.get_current_timestamp(),
                "version": settings.VERSION
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"User creation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"User creation failed: {str(e)}"
        )


async def get_or_create_firebase_user(firebase_user: FirebaseUser) -> User:
    """Get or create user from Firebase user information, ensuring user exists in both Firebase and MongoDB."""
    try:
        # Ensure MongoDB is initialized
        from app.core.mongodb import ensure_mongodb_initialized
        await ensure_mongodb_initialized()
        
        if not firebase_user.email or not firebase_user.uid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid Firebase user information - missing email or uid"
            )
        
        # Check if user exists in Firebase (verify the Firebase user still exists)
        if firebase_auth.is_available():
            firebase_user_exists = await firebase_auth.get_firebase_user(firebase_user.uid)
            if not firebase_user_exists:
                # If Firebase user doesn't exist, create them
                try:
                    await firebase_auth.create_firebase_user_with_provider(
                        email=firebase_user.email,
                        google_id=firebase_user.uid if firebase_user.provider == "google" else None,
                        display_name=firebase_user.name,
                        photo_url=firebase_user.picture,
                        email_verified=firebase_user.email_verified,
                        provider=firebase_user.provider
                    )
                    logger.info(f"Created missing Firebase user: {firebase_user.uid}")
                except Exception as e:
                    logger.warning(f"Failed to create Firebase user (may already exist): {str(e)}")
        
        # Try to find existing user by email or Firebase UID
        user = await User.find_one({
            "$or": [
                {"email": firebase_user.email},
                {"firebase_uid": firebase_user.uid}
            ]
        })
        
        if user:
            # Update existing user information
            updated = False
            
            # Update Firebase UID if not set or different
            if not user.firebase_uid or user.firebase_uid != firebase_user.uid:
                user.firebase_uid = firebase_user.uid
                updated = True
            
            # Update auth provider
            if user.auth_provider != firebase_user.provider:
                user.auth_provider = firebase_user.provider
                updated = True
            
            # Update verification status
            if user.is_verified != firebase_user.email_verified:
                user.is_verified = firebase_user.email_verified
                updated = True
            
            # Update full name if not set and available from Firebase
            if firebase_user.name and not user.full_name:
                user.full_name = firebase_user.name
                updated = True
            
            # Update avatar URL if not set and available from Firebase
            if firebase_user.picture and not user.avatar_url:
                user.avatar_url = firebase_user.picture
                updated = True
            
            # Save changes if any updates were made
            if updated:
                user.updated_at = datetime.utcnow()
                await user.save_changes()
            
            logger.info(f"Updated existing user for Firebase UID: {firebase_user.uid}")
            return user
        else:
            # Create new user in MongoDB
            username = firebase_user.email.split("@")[0]
            
            # Ensure username is unique
            counter = 1
            original_username = username
            while await User.find_one({"username": username}):
                username = f"{original_username}{counter}"
                counter += 1
            
            new_user = User(
                email=firebase_user.email,
                username=username,
                full_name=firebase_user.name,
                avatar_url=firebase_user.picture,
                firebase_uid=firebase_user.uid,
                auth_provider=firebase_user.provider,
                is_verified=firebase_user.email_verified,
                is_active=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            await new_user.insert()
            logger.info(f"Created new MongoDB user for Firebase UID: {firebase_user.uid}")
            return new_user
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create/update Firebase user {firebase_user.uid}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create/update Firebase user: {str(e)}"
        )


async def get_or_create_google_user(google_user_info: dict) -> User:
    """Get or create user from Google user information, ensuring user exists in both Firebase and MongoDB."""
    try:
        # Ensure MongoDB is initialized
        from app.core.mongodb import ensure_mongodb_initialized
        await ensure_mongodb_initialized()
        
        email = google_user_info.get("email")
        google_id = google_user_info.get("id")
        
        if not email or not google_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid Google user information"
            )
        
        # Try to find existing user by email or Google ID
        user = await User.find_one({
            "$or": [
                {"email": email},
                {"firebase_uid": google_id}
            ]
        })
        
        firebase_uid = None
        
        if user:
            # Update user information if changed
            firebase_uid = user.firebase_uid or google_id
            user.firebase_uid = firebase_uid
            user.auth_provider = "google"
            user.is_verified = google_user_info.get("verified_email", False)
            
            if google_user_info.get("name") and not user.full_name:
                user.full_name = google_user_info.get("name")
            
            if google_user_info.get("picture") and not user.avatar_url:
                user.avatar_url = google_user_info.get("picture")
            
            user.updated_at = datetime.utcnow()
            await user.save_changes()
            
            logger.info(f"Updated existing Google user: {email}")
        else:
            # Create new user in MongoDB first
            username = email.split("@")[0]
            
            # Ensure username is unique
            counter = 1
            original_username = username
            while await User.find_one({"username": username}):
                username = f"{original_username}{counter}"
                counter += 1
            
            # Use Google ID as Firebase UID for OAuth users
            firebase_uid = google_id
            
            new_user = User(
                email=email,
                username=username,
                full_name=google_user_info.get("name"),
                avatar_url=google_user_info.get("picture"),
                firebase_uid=firebase_uid,
                auth_provider="google",
                is_verified=google_user_info.get("verified_email", False),
                is_active=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            await new_user.insert()
            user = new_user
            logger.info(f"Created new MongoDB user for Google OAuth: {email}")
        
        # Now ensure user exists in Firebase (if Firebase is available)
        if firebase_auth.is_available() and firebase_uid:
            try:
                # Check if Firebase user exists
                existing_firebase_user = await firebase_auth.get_firebase_user(firebase_uid)
                if not existing_firebase_user:
                    # Create Firebase user with Google provider data
                    created_firebase_uid = await firebase_auth.create_firebase_user_with_provider(
                        email=email,
                        google_id=google_id,
                        display_name=google_user_info.get("name"),
                        photo_url=google_user_info.get("picture"),
                        email_verified=google_user_info.get("verified_email", False),
                        provider="google"
                    )
                    logger.info(f"Created Firebase user for Google OAuth: {email} with UID: {created_firebase_uid}")
                    
                    # Update MongoDB user with the actual Firebase UID if different
                    if created_firebase_uid != firebase_uid:
                        user.firebase_uid = created_firebase_uid
                        await user.save_changes()
                else:
                    logger.info(f"Firebase user already exists for Google OAuth: {email}")
            except Exception as e:
                logger.warning(f"Failed to create/verify Firebase user for Google OAuth {email}: {str(e)}")
                # Don't fail the whole process if Firebase user creation fails
        
        return user
            
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        full_error = traceback.format_exc()
        logger.error(f"Failed to create/update Google user {email}: {str(e)}")
        logger.error(f"Full traceback: {full_error}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create/update Google user: {str(e)}"
        )