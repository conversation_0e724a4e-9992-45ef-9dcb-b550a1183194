"""
Main FastAPI application instance with middleware and router configuration.
"""

from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException
from starlette.middleware.gzip import GZipMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
import logging
import os

from app.core.config import settings
from app.core.mongodb import connect_to_mongo, close_mongo_connection
from app.routers import health, auth, user
from app.debug_handler import debug_router

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL.upper()),
    format=settings.LOG_FORMAT,
    handlers=[
        logging.StreamHandler()  # This ensures logs go to stdout/stderr for Docker
    ]
)

# Get logger for this module
logger = logging.getLogger(__name__)

# Create FastAPI application instance
app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.PROJECT_DESCRIPTION,
    version=settings.VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=f"{settings.API_V1_STR}/docs",
    redoc_url=f"{settings.API_V1_STR}/redoc",
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS,
)

app.add_middleware(GZipMiddleware, minimum_size=1000)


# Lambda initialization middleware
class LambdaInitMiddleware(BaseHTTPMiddleware):
    """Middleware to ensure MongoDB initialization in Lambda environment."""
    
    def __init__(self, app):
        super().__init__(app)
        self.initialized = False
    
    async def dispatch(self, request: Request, call_next):
        """Ensure initialization before processing request."""
        logger.info(f"🔄 Middleware triggered. Initialized: {self.initialized}, Path: {request.url.path}")
        
        if not self.initialized:
            try:
                logger.info("🔄 Initializing MongoDB for Lambda...")
                from app.core.mongodb import connect_to_mongo
                await connect_to_mongo()
                self.initialized = True
                logger.info("✅ MongoDB initialized successfully in Lambda")
            except Exception as e:
                import traceback
                full_error = traceback.format_exc()
                logger.error(f"❌ Failed to initialize MongoDB in Lambda: {str(e)}")
                logger.error(f"Full traceback: {full_error}")
                return JSONResponse(
                    status_code=500,
                    content={
                        "success": False,
                        "error": {
                            "code": "INITIALIZATION_ERROR",
                            "message": f"Failed to initialize database: {str(e)}",
                            "details": []
                        },
                        "meta": {
                            "timestamp": settings.get_current_timestamp(),
                            "version": settings.VERSION
                        }
                    }
                )
        
        response = await call_next(request)
        return response

# Add Lambda initialization middleware (only in Lambda environment)
is_lambda = bool(os.getenv("LAMBDA_TASK_ROOT"))
logger.info(f"🔍 Lambda environment detected: {is_lambda}, LAMBDA_TASK_ROOT: {os.getenv('LAMBDA_TASK_ROOT')}")

if is_lambda:
    logger.info("🔧 Adding Lambda initialization middleware")
    app.add_middleware(LambdaInitMiddleware)
else:
    logger.info("🏠 Running in local environment, skipping Lambda middleware")


# Exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Handle HTTP exceptions with consistent response format."""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "error": {
                "code": f"HTTP_{exc.status_code}",
                "message": exc.detail,
                "details": []
            },
            "meta": {
                "timestamp": settings.get_current_timestamp(),
                "version": settings.VERSION
            }
        }
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request, exc):
    """Handle validation errors with consistent response format."""
    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "error": {
                "code": "VALIDATION_ERROR",
                "message": "Validation failed",
                "details": exc.errors()
            },
            "meta": {
                "timestamp": settings.get_current_timestamp(),
                "version": settings.VERSION
            }
        }
    )


# Event handlers
@app.on_event("startup")
async def startup_event():
    """Initialize application on startup."""
    # Connect to MongoDB
    await connect_to_mongo()
    logger.info(f"🚀 {settings.PROJECT_NAME} v{settings.VERSION} started successfully!")


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on application shutdown."""
    # Close MongoDB connection
    await close_mongo_connection()
    logger.info(f"🛑 {settings.PROJECT_NAME} shutting down...")


# Include routers
app.include_router(
    health.router,
    prefix=settings.API_V1_STR,
    tags=["Health"]
)

app.include_router(
    auth.router,
    prefix=f"{settings.API_V1_STR}/auth",
    tags=["Authentication"]
)

app.include_router(
    user.router,
    prefix=f"{settings.API_V1_STR}/users",
    tags=["Users"]
)

# Debug router (only in development)
if settings.DEBUG:
    app.include_router(
        debug_router,
        prefix=settings.API_V1_STR,
        tags=["Debug"]
    )



# Root endpoint
@app.get("/")
async def root():
    """Root endpoint providing API information."""
    return {
        "success": True,
        "message": f"Welcome to {settings.PROJECT_NAME} API",
        "data": {
            "name": settings.PROJECT_NAME,
            "version": settings.VERSION,
            "description": settings.PROJECT_DESCRIPTION,
            "docs_url": f"{settings.API_V1_STR}/docs",
            "openapi_url": f"{settings.API_V1_STR}/openapi.json"
        },
        "meta": {
            "timestamp": settings.get_current_timestamp(),
            "version": settings.VERSION
        }
    }