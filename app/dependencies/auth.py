"""
Authentication dependencies for FastAPI endpoints using MongoDB.
"""

from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from app.core.security import verify_token
from app.core.firebase_auth import firebase_auth, FirebaseUser
from app.core.cognito_auth import cognito_auth, CognitoUser
from app.core.google_auth import google_auth, GoogleUser
from app.core.mongodb import mongodb_client
from app.models.user import User
from app.schemas.user import TokenData

# HTTP Bearer token scheme
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """
    Get current user from either JWT token or Firebase token (flexible authentication).
    
    Args:
        credentials: HTTP Bearer credentials
        
    Returns:
        User: Current authenticated user
        
    Raises:
        HTTPException: If token is invalid or user not found
    """
    return await get_current_user_flexible(credentials)


async def get_or_create_firebase_user(firebase_user: FirebaseUser) -> User:
    """
    Get or create a user from Firebase authentication data.
    
    Args:
        firebase_user: Firebase user data
        
    Returns:
        User: Database user instance or mock user if database not available
    """
    # Check if MongoDB is available
    if not mongodb_client:
        # Return a mock user for demonstration purposes
        from datetime import datetime
        return User.construct(
            id=firebase_user.uid,
            email=firebase_user.email,
            username=firebase_user.email.split('@')[0] if firebase_user.email else f"user_{firebase_user.uid[:8]}",
            full_name=firebase_user.name,
            firebase_uid=firebase_user.uid,
            auth_provider=firebase_user.provider,
            is_active=True,
            is_verified=firebase_user.email_verified,
            avatar_url=firebase_user.picture,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
    
    try:
        # Try to find user by Firebase UID
        user = await User.find_by_firebase_uid(firebase_user.uid)
        
        if user:
            # Update user information if needed
            if firebase_user.email and user.email != firebase_user.email:
                user.email = firebase_user.email
            if firebase_user.name and user.full_name != firebase_user.name:
                user.full_name = firebase_user.name
            if firebase_user.picture and user.avatar_url != firebase_user.picture:
                user.avatar_url = firebase_user.picture
            
            user.is_verified = firebase_user.email_verified
            user.auth_provider = firebase_user.provider
            await user.save_changes()
            return user
        
        # Try to find user by email if it exists
        if firebase_user.email:
            user = await User.find_by_email(firebase_user.email)
            if user:
                # Link existing user to Firebase
                user.firebase_uid = firebase_user.uid
                user.auth_provider = firebase_user.provider
                user.is_verified = firebase_user.email_verified
                if firebase_user.name and not user.full_name:
                    user.full_name = firebase_user.name
                if firebase_user.picture and not user.avatar_url:
                    user.avatar_url = firebase_user.picture
                await user.save_changes()
                return user
        
        # Create new user
        username = firebase_user.email.split('@')[0] if firebase_user.email else f"user_{firebase_user.uid[:8]}"
        
        # Ensure username is unique
        base_username = username
        counter = 1
        while await User.find_by_username(username):
            username = f"{base_username}_{counter}"
            counter += 1
        
        user = User(
            email=firebase_user.email,
            username=username,
            full_name=firebase_user.name,
            firebase_uid=firebase_user.uid,
            auth_provider=firebase_user.provider,
            is_active=True,
            is_verified=firebase_user.email_verified,
            avatar_url=firebase_user.picture
        )
        
        await user.save()
        return user
        
    except Exception as e:
        # If database operations fail, return a mock user
        from datetime import datetime
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"Database operations failed, returning mock user: {str(e)}")
        
        return User.construct(
            id=firebase_user.uid,
            email=firebase_user.email,
            username=firebase_user.email.split('@')[0] if firebase_user.email else f"user_{firebase_user.uid[:8]}",
            full_name=firebase_user.name,
            firebase_uid=firebase_user.uid,
            auth_provider=firebase_user.provider,
            is_active=True,
            is_verified=firebase_user.email_verified,
            avatar_url=firebase_user.picture,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )


async def get_or_create_cognito_user(cognito_user: CognitoUser) -> User:
    """
    Get or create a user from AWS Cognito authentication data.
    
    Args:
        cognito_user: Cognito user data
        
    Returns:
        User: Database user instance or mock user if database not available
    """
    # Check if MongoDB is available
    if not mongodb_client:
        # Return a mock user for demonstration purposes
        from datetime import datetime
        return User.construct(
            id=cognito_user.sub,
            email=cognito_user.email,
            username=cognito_user.username,
            full_name=cognito_user.name,
            firebase_uid=None,
            auth_provider="cognito",
            is_active=True,
            is_verified=cognito_user.email_verified,
            avatar_url=None,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
    
    try:
        # Try to find user by Cognito sub (user ID)
        user = await User.find_one({"cognito_sub": cognito_user.sub})
        
        if user:
            # Update user information if needed
            if cognito_user.email and user.email != cognito_user.email:
                user.email = cognito_user.email
            if cognito_user.name and user.full_name != cognito_user.name:
                user.full_name = cognito_user.name
            
            user.is_verified = cognito_user.email_verified
            user.auth_provider = "cognito"
            await user.save_changes()
            return user
        
        # Try to find user by email if it exists
        if cognito_user.email:
            user = await User.find_by_email(cognito_user.email)
            if user:
                # Link existing user to Cognito
                user.cognito_sub = cognito_user.sub
                user.auth_provider = "cognito"
                user.is_verified = cognito_user.email_verified
                if cognito_user.name and not user.full_name:
                    user.full_name = cognito_user.name
                await user.save_changes()
                return user
        
        # Create new user
        username = cognito_user.username or cognito_user.email.split('@')[0] if cognito_user.email else f"user_{cognito_user.sub[:8]}"
        
        # Ensure username is unique
        base_username = username
        counter = 1
        while await User.find_by_username(username):
            username = f"{base_username}_{counter}"
            counter += 1
        
        user = User(
            email=cognito_user.email,
            username=username,
            full_name=cognito_user.name,
            cognito_sub=cognito_user.sub,
            auth_provider="cognito",
            is_active=True,
            is_verified=cognito_user.email_verified
        )
        
        await user.save()
        return user
        
    except Exception as e:
        # If database operations fail, return a mock user
        from datetime import datetime
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"Database operations failed, returning mock user: {str(e)}")
        
        return User.construct(
            id=cognito_user.sub,
            email=cognito_user.email,
            username=cognito_user.username,
            full_name=cognito_user.name,
            firebase_uid=None,
            auth_provider="cognito",
            is_active=True,
            is_verified=cognito_user.email_verified,
            avatar_url=None,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )


async def get_or_create_google_user(google_user: GoogleUser) -> User:
    """
    Get or create a user from Google OAuth authentication data.
    
    Args:
        google_user: Google user data
        
    Returns:
        User: Database user instance or mock user if database not available
    """
    # Check if MongoDB is available
    if not mongodb_client:
        # Return a mock user for demonstration purposes
        from datetime import datetime
        return User.construct(
            id=google_user.sub,
            email=google_user.email,
            username=google_user.email.split('@')[0] if google_user.email else f"user_{google_user.sub[:8]}",
            full_name=google_user.name,
            firebase_uid=None,
            auth_provider="google",
            is_active=True,
            is_verified=google_user.email_verified,
            avatar_url=google_user.picture,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
    
    try:
        # Try to find user by Google sub (user ID) or email
        user = await User.find_one({
            "$or": [
                {"google_sub": google_user.sub},
                {"email": google_user.email} if google_user.email else {"google_sub": google_user.sub}
            ]
        })
        
        if user:
            # Update user information if needed
            if google_user.email and user.email != google_user.email:
                user.email = google_user.email
            if google_user.name and user.full_name != google_user.name:
                user.full_name = google_user.name
            if google_user.picture and user.avatar_url != google_user.picture:
                user.avatar_url = google_user.picture
            
            user.google_sub = google_user.sub
            user.is_verified = google_user.email_verified
            user.auth_provider = "google"
            await user.save_changes()
            return user
        
        # Create new user
        username = google_user.email.split('@')[0] if google_user.email else f"user_{google_user.sub[:8]}"
        
        # Ensure username is unique
        base_username = username
        counter = 1
        while await User.find_by_username(username):
            username = f"{base_username}_{counter}"
            counter += 1
        
        user = User(
            email=google_user.email,
            username=username,
            full_name=google_user.name,
            google_sub=google_user.sub,
            auth_provider="google",
            is_active=True,
            is_verified=google_user.email_verified,
            avatar_url=google_user.picture
        )
        
        await user.save()
        return user
        
    except Exception as e:
        # If database operations fail, return a mock user
        from datetime import datetime
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"Database operations failed, returning mock user: {str(e)}")
        
        return User.construct(
            id=google_user.sub,
            email=google_user.email,
            username=google_user.email.split('@')[0] if google_user.email else f"user_{google_user.sub[:8]}",
            full_name=google_user.name,
            firebase_uid=None,
            auth_provider="google",
            is_active=True,
            is_verified=google_user.email_verified,
            avatar_url=google_user.picture,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )


async def get_current_user_flexible(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """
    Get current user from JWT token, Firebase token, or Cognito token.
    
    Args:
        credentials: HTTP Bearer credentials
        
    Returns:
        User: Current authenticated user
        
    Raises:
        HTTPException: If token is invalid or user not found
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    token = credentials.credentials
    
    # Try Firebase authentication first (primary system)
    try:
        firebase_user = await firebase_auth.verify_firebase_token(token)
        user = await get_or_create_firebase_user(firebase_user)
        return user
    except HTTPException:
        # If Firebase fails, continue to next authentication method
        pass
    except Exception:
        # Catch any other Firebase errors and continue
        pass
    
    # Try Google OAuth authentication
    try:
        google_user = await google_auth.verify_google_token(token)
        user = await get_or_create_google_user(google_user)
        return user
    except HTTPException:
        # If Google OAuth fails, continue to next authentication method
        pass
    except Exception:
        # Catch any other Google OAuth errors and continue
        pass
    
    # Try Cognito authentication (if configured)
    try:
        cognito_user = await cognito_auth.verify_cognito_token(token)
        user = await get_or_create_cognito_user(cognito_user)
        return user
    except HTTPException:
        # If Cognito fails, continue to next authentication method
        pass
    except Exception:
        # Catch any other Cognito errors and continue
        pass
    
    # Try JWT authentication
    try:
        payload = verify_token(token)
        username: str = payload.get("sub")
        user_id: str = payload.get("user_id")
        
        if username is None or user_id is None:
            raise credentials_exception
            
        token_data = TokenData(username=username, user_id=user_id)
        
        # Get user from database
        if mongodb_client:
            try:
                user = await User.get(user_id)
                if user is None:
                    # User not found in database, create mock user
                    from datetime import datetime
                    mock_user = User.construct(
                        id=user_id,
                        email=f"user_{user_id}@example.com",
                        username=username,
                        firebase_uid=None,
                        auth_provider="jwt",
                        is_active=True,
                        is_verified=True,
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow()
                    )
                    return mock_user
                return user
            except Exception as db_error:
                # If database lookup fails, create a mock user for demo
                from datetime import datetime
                mock_user = User.construct(
                    id=user_id,
                    email=f"user_{user_id}@example.com",
                    username=username,
                    firebase_uid=None,
                    auth_provider="jwt",
                    is_active=True,
                    is_verified=True,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                return mock_user
        else:
            # Create a mock user for demo when database not available
            from datetime import datetime
            mock_user = User.construct(
                id=user_id,
                email=f"user_{user_id}@example.com",
                username=username,
                firebase_uid=None,
                auth_provider="jwt",
                is_active=True,
                is_verified=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            return mock_user
        
    except HTTPException:
        # Re-raise HTTP exceptions (like JWT verification errors)
        raise
    except Exception as e:
        # Log the actual error for debugging
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Authentication error: {str(e)}")
        raise credentials_exception


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current active user.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        User: Current active user
        
    Raises:
        HTTPException: If user is not active
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    return current_user


async def get_current_verified_user(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """
    Get current verified user.
    
    Args:
        current_user: Current active user
        
    Returns:
        User: Current verified user
        
    Raises:
        HTTPException: If user is not verified
    """
    if not current_user.is_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email not verified"
        )
    
    return current_user


async def get_current_superuser(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """
    Get current superuser.
    
    Args:
        current_user: Current active user
        
    Returns:
        User: Current superuser
        
    Raises:
        HTTPException: If user is not a superuser
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return current_user


def require_permissions(required_permission: str):
    """
    Dependency factory for requiring specific permissions.
    
    Args:
        required_permission: Permission required
        
    Returns:
        Callable: Dependency function
    """
    async def permission_checker(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        """
        Check if user has required permission.
        
        Args:
            current_user: Current active user
            
        Returns:
            User: User with required permission
            
        Raises:
            HTTPException: If user lacks permission
        """
        if not current_user.check_permissions(required_permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{required_permission}' required"
            )
        
        return current_user
    
    return permission_checker


def require_resource_access(resource_owner_id: str):
    """
    Dependency factory for requiring resource access.
    
    Args:
        resource_owner_id: ID of the resource owner
        
    Returns:
        Callable: Dependency function
    """
    async def resource_access_checker(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        """
        Check if user can access resource.
        
        Args:
            current_user: Current active user
            
        Returns:
            User: User with resource access
            
        Raises:
            HTTPException: If user lacks access
        """
        if not current_user.can_access_resource(resource_owner_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this resource"
            )
        
        return current_user
    
    return resource_access_checker