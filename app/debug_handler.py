"""
Debug handler for troubleshooting SAM local issues.
"""

import logging
from typing import Any, Dict
from fastapi import APIRouter, HTTPException
from app.core.config import settings
from app.core.mongodb import mongodb_client, get_database

logger = logging.getLogger(__name__)

debug_router = APIRouter()


@debug_router.get("/debug/config")
async def debug_config():
    """Debug endpoint to check configuration values."""
    config_info = {
        "stage": settings.STAGE,
        "mongodb_url": settings.MONGODB_URL,
        "mongodb_database": settings.MONGODB_DATABASE,
        "google_client_id_set": bool(settings.GOOGLE_CLIENT_ID),
        "google_client_secret_set": bool(settings.GOOGLE_CLIENT_SECRET),
        "google_redirect_uri": settings.GOOGLE_REDIRECT_URI,
        "firebase_project_id": settings.FIREBASE_PROJECT_ID,
        "aws_region": settings.AWS_REGION,
        "debug": settings.DEBUG,
        "project_name": settings.PROJECT_NAME
    }
    
    return {
        "success": True,
        "message": "Configuration debug info",
        "data": config_info
    }


@debug_router.get("/debug/mongodb")
async def debug_mongodb():
    """Debug endpoint to check MongoDB connection."""
    try:
        # Try to ensure MongoDB is initialized
        from app.core.mongodb import ensure_mongodb_initialized, is_mongodb_initialized
        import os
        
        # Check current state
        is_initialized = is_mongodb_initialized()
        is_lambda = bool(os.getenv("LAMBDA_TASK_ROOT"))
        
        logger.info(f"🔍 Debug MongoDB: initialized={is_initialized}, is_lambda={is_lambda}")
        
        if not is_initialized:
            logger.info("MongoDB not initialized, attempting to initialize...")
            try:
                await ensure_mongodb_initialized()
                logger.info("✅ MongoDB initialization completed via debug endpoint")
            except Exception as init_error:
                logger.error(f"❌ MongoDB initialization failed via debug endpoint: {str(init_error)}")
                import traceback
                logger.error(f"Full traceback: {traceback.format_exc()}")
        
        if mongodb_client is None:
            return {
                "success": False,
                "message": "MongoDB client not initialized",
                "data": {
                    "connected": False,
                    "error": "MongoDB client is None"
                }
            }
        
        # Test ping
        await mongodb_client.admin.command('ping')
        
        # Test database access
        database = get_database()
        if database is None:
            return {
                "success": False,
                "message": "Database instance is None",
                "data": {"connected": False}
            }
        
        # Test collection access
        collections = await database.list_collection_names()
        
        return {
            "success": True,
            "message": "MongoDB connection successful",
            "data": {
                "connected": True,
                "database_name": settings.MONGODB_DATABASE,
                "collections": collections,
                "mongodb_url_preview": settings.MONGODB_URL[:50] + "..." if len(settings.MONGODB_URL) > 50 else settings.MONGODB_URL
            }
        }
        
    except Exception as e:
        logger.error(f"MongoDB debug failed: {str(e)}")
        return {
            "success": False,
            "message": f"MongoDB connection failed: {str(e)}",
            "data": {
                "connected": False,
                "error": str(e),
                "mongodb_url_preview": settings.MONGODB_URL[:50] + "..." if len(settings.MONGODB_URL) > 50 else settings.MONGODB_URL
            }
        }


@debug_router.get("/debug/mongodb-test")
async def debug_mongodb_test():
    """Direct MongoDB connection test for debugging."""
    import traceback
    import os
    from app.core.config import settings
    
    results = {
        "environment": {
            "is_lambda": bool(os.getenv("LAMBDA_TASK_ROOT")),
            "lambda_task_root": os.getenv("LAMBDA_TASK_ROOT"),
            "stage": settings.STAGE,
            "mongodb_url_preview": settings.MONGODB_URL[:50] + "..." if len(settings.MONGODB_URL) > 50 else settings.MONGODB_URL,
            "mongodb_database": settings.MONGODB_DATABASE
        },
        "connection_test": None,
        "beanie_test": None
    }
    
    # Test 1: Direct MongoDB connection
    try:
        logger.info("🔍 Testing direct MongoDB connection...")
        from motor.motor_asyncio import AsyncIOMotorClient
        
        test_client = AsyncIOMotorClient(settings.MONGODB_URL)
        await test_client.admin.command('ping')
        
        results["connection_test"] = {
            "success": True,
            "message": "Direct MongoDB connection successful"
        }
        logger.info("✅ Direct MongoDB connection successful")
        
        # Test 2: Beanie initialization
        try:
            logger.info("🔍 Testing Beanie initialization...")
            from beanie import init_beanie
            from app.models.user import User
            
            database = test_client[settings.MONGODB_DATABASE]
            await init_beanie(database=database, document_models=[User])
            
            results["beanie_test"] = {
                "success": True,
                "message": "Beanie initialization successful"
            }
            logger.info("✅ Beanie initialization successful")
            
            # Test 3: User model operation
            try:
                # Try a simple query
                user_count = await User.count()
                results["beanie_test"]["user_count"] = user_count
                logger.info(f"✅ User model test successful, user count: {user_count}")
                
            except Exception as user_error:
                results["beanie_test"]["user_error"] = str(user_error)
                logger.error(f"❌ User model test failed: {str(user_error)}")
                
        except Exception as beanie_error:
            results["beanie_test"] = {
                "success": False,
                "error": str(beanie_error),
                "traceback": traceback.format_exc()
            }
            logger.error(f"❌ Beanie initialization failed: {str(beanie_error)}")
        
        # Close test client
        test_client.close()
        
    except Exception as conn_error:
        results["connection_test"] = {
            "success": False,
            "error": str(conn_error),
            "traceback": traceback.format_exc()
        }
        logger.error(f"❌ Direct MongoDB connection failed: {str(conn_error)}")
    
    return {
        "success": True,
        "message": "MongoDB connection test completed",
        "data": results
    }


@debug_router.get("/debug/environment")
async def debug_environment():
    """Debug endpoint to check environment and runtime info."""
    import os
    import sys
    
    env_info = {
        "python_version": sys.version,
        "stage": os.getenv("STAGE", "not_set"),
        "lambda_task_root": os.getenv("LAMBDA_TASK_ROOT", "not_set"),
        "aws_region": os.getenv("AWS_REGION", "not_set"),
        "aws_access_key_id_set": bool(os.getenv("AWS_ACCESS_KEY_ID")),
        "aws_secret_access_key_set": bool(os.getenv("AWS_SECRET_ACCESS_KEY")),
        "current_working_directory": os.getcwd(),
        "python_path": sys.path[:5],  # First 5 entries
    }
    
    return {
        "success": True,
        "message": "Environment debug info",
        "data": env_info
    } 