"""
Base Pydantic schemas for common response formats.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field


class BaseResponse(BaseModel):
    """
    Base response schema for consistent API responses.
    
    Attributes:
        success: Whether the operation was successful
        message: Human-readable message
        data: Response data payload
        meta: Metadata about the response
    """
    
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Human-readable message")
    data: Optional[Union[Dict[str, Any], List[Any], Any]] = Field(
        None, 
        description="Response data payload"
    )
    meta: Optional[Dict[str, Any]] = Field(
        None, 
        description="Metadata about the response"
    )
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat() + "Z"
        }


class ErrorDetail(BaseModel):
    """
    Error detail schema for validation errors.
    
    Attributes:
        field: Field that caused the error
        message: Error message
        code: Error code
    """
    
    field: Optional[str] = Field(None, description="Field that caused the error")
    message: str = Field(..., description="Error message")
    code: Optional[str] = Field(None, description="Error code")


class ErrorResponse(BaseModel):
    """
    Error response schema for consistent error handling.
    
    Attributes:
        success: Always False for error responses
        error: Error information
        meta: Metadata about the response
    """
    
    success: bool = Field(False, description="Always False for error responses")
    error: Dict[str, Any] = Field(..., description="Error information")
    meta: Optional[Dict[str, Any]] = Field(
        None, 
        description="Metadata about the response"
    )
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat() + "Z"
        }


class PaginationMeta(BaseModel):
    """
    Pagination metadata schema.
    
    Attributes:
        page: Current page number
        page_size: Number of items per page
        total_items: Total number of items
        total_pages: Total number of pages
        has_next: Whether there's a next page
        has_previous: Whether there's a previous page
    """
    
    page: int = Field(..., description="Current page number", ge=1)
    page_size: int = Field(..., description="Number of items per page", ge=1, le=100)
    total_items: int = Field(..., description="Total number of items", ge=0)
    total_pages: int = Field(..., description="Total number of pages", ge=0)
    has_next: bool = Field(..., description="Whether there's a next page")
    has_previous: bool = Field(..., description="Whether there's a previous page")


class PaginatedResponse(BaseResponse):
    """
    Paginated response schema.
    
    Attributes:
        data: List of items
        pagination: Pagination metadata
    """
    
    data: List[Any] = Field(..., description="List of items")
    pagination: PaginationMeta = Field(..., description="Pagination metadata")


class HealthCheck(BaseModel):
    """
    Health check response schema.
    
    Attributes:
        status: Service status
        timestamp: Current timestamp
        version: API version
        uptime: Service uptime in seconds
        database: Database connection status
    """
    
    status: str = Field(..., description="Service status")
    timestamp: datetime = Field(..., description="Current timestamp")
    version: str = Field(..., description="API version")
    uptime: Optional[float] = Field(None, description="Service uptime in seconds")
    database: Optional[Dict[str, Any]] = Field(None, description="Database connection status")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat() + "Z"
        }


class MessageResponse(BaseModel):
    """
    Simple message response schema.
    
    Attributes:
        message: Response message
    """
    
    message: str = Field(..., description="Response message")