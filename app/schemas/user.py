"""
User-related Pydantic schemas for request/response validation.
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, EmailStr, validator
import re


class UserBase(BaseModel):
    """
    Base user schema with common fields.
    
    Attributes:
        email: User's email address
        username: User's username
        full_name: User's full name
        bio: User's biography
        avatar_url: URL to user's avatar
    """
    
    email: EmailStr = Field(..., description="User's email address")
    username: str = Field(..., min_length=3, max_length=50, description="User's username")
    full_name: Optional[str] = Field(None, max_length=100, description="User's full name")
    bio: Optional[str] = Field(None, max_length=500, description="User's biography")
    avatar_url: Optional[str] = Field(None, max_length=500, description="URL to user's avatar")
    
    @validator('username')
    def validate_username(cls, v):
        """Validate username format."""
        if not re.match(r'^[a-zA-Z0-9_]+$', v):
            raise ValueError('Username can only contain letters, numbers, and underscores')
        return v.lower()
    
    @validator('email')
    def validate_email(cls, v):
        """Validate and normalize email."""
        return v.lower()


class UserCreate(UserBase):
    """
    Schema for creating a new user.
    
    Attributes:
        password: User's password
        confirm_password: Password confirmation
    """
    
    password: str = Field(..., min_length=8, max_length=100, description="User's password")
    confirm_password: str = Field(..., description="Password confirmation")
    
    @validator('password')
    def validate_password(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        
        if not re.search(r'[A-Za-z]', v):
            raise ValueError('Password must contain at least one letter')
        
        if not re.search(r'[0-9]', v):
            raise ValueError('Password must contain at least one digit')
        
        return v
    
    @validator('confirm_password')
    def validate_passwords_match(cls, v, values):
        """Validate that passwords match."""
        if 'password' in values and v != values['password']:
            raise ValueError('Passwords do not match')
        return v


class UserUpdate(BaseModel):
    """
    Schema for updating user information.
    
    Attributes:
        full_name: User's full name
        bio: User's biography
        avatar_url: URL to user's avatar
    """
    
    full_name: Optional[str] = Field(None, max_length=100, description="User's full name")
    bio: Optional[str] = Field(None, max_length=500, description="User's biography")
    avatar_url: Optional[str] = Field(None, max_length=500, description="URL to user's avatar")


class UserResponse(BaseModel):
    """
    Schema for user response data.
    
    Attributes:
        id: User ID
        email: User's email address
        username: User's username
        full_name: User's full name
        bio: User's biography
        avatar_url: URL to user's avatar
        is_active: Whether the user is active
        is_verified: Whether the user's email is verified
        created_at: User creation timestamp
        updated_at: Last update timestamp
        last_login: Last login timestamp
    """
    
    id: int = Field(..., description="User ID")
    email: EmailStr = Field(..., description="User's email address")
    username: str = Field(..., description="User's username")
    full_name: Optional[str] = Field(None, description="User's full name")
    bio: Optional[str] = Field(None, description="User's biography")
    avatar_url: Optional[str] = Field(None, description="URL to user's avatar")
    is_active: bool = Field(..., description="Whether the user is active")
    is_verified: bool = Field(..., description="Whether the user's email is verified")
    created_at: datetime = Field(..., description="User creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    last_login: Optional[datetime] = Field(None, description="Last login timestamp")
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() + "Z"
        }


class UserLogin(BaseModel):
    """
    Schema for user login request.
    
    Attributes:
        username: Username or email
        password: User's password
    """
    
    username: str = Field(..., description="Username or email")
    password: str = Field(..., description="User's password")


class UserPasswordChange(BaseModel):
    """
    Schema for changing user password.
    
    Attributes:
        current_password: Current password
        new_password: New password
        confirm_new_password: New password confirmation
    """
    
    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=8, max_length=100, description="New password")
    confirm_new_password: str = Field(..., description="New password confirmation")
    
    @validator('new_password')
    def validate_new_password(cls, v):
        """Validate new password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        
        if not re.search(r'[A-Za-z]', v):
            raise ValueError('Password must contain at least one letter')
        
        if not re.search(r'[0-9]', v):
            raise ValueError('Password must contain at least one digit')
        
        return v
    
    @validator('confirm_new_password')
    def validate_passwords_match(cls, v, values):
        """Validate that passwords match."""
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('Passwords do not match')
        return v


class UserPasswordReset(BaseModel):
    """
    Schema for password reset request.
    
    Attributes:
        email: User's email address
    """
    
    email: EmailStr = Field(..., description="User's email address")


class UserPasswordResetConfirm(BaseModel):
    """
    Schema for password reset confirmation.
    
    Attributes:
        token: Password reset token
        new_password: New password
        confirm_new_password: New password confirmation
    """
    
    token: str = Field(..., description="Password reset token")
    new_password: str = Field(..., min_length=8, max_length=100, description="New password")
    confirm_new_password: str = Field(..., description="New password confirmation")
    
    @validator('new_password')
    def validate_new_password(cls, v):
        """Validate new password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        
        if not re.search(r'[A-Za-z]', v):
            raise ValueError('Password must contain at least one letter')
        
        if not re.search(r'[0-9]', v):
            raise ValueError('Password must contain at least one digit')
        
        return v
    
    @validator('confirm_new_password')
    def validate_passwords_match(cls, v, values):
        """Validate that passwords match."""
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('Passwords do not match')
        return v


class Token(BaseModel):
    """
    Schema for JWT token response.
    
    Attributes:
        access_token: JWT access token
        token_type: Token type (usually "bearer")
        expires_in: Token expiration time in seconds
    """
    
    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field("bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")


class TokenData(BaseModel):
    """
    Schema for JWT token data.
    
    Attributes:
        username: Username from token
        user_id: User ID from token
    """
    
    username: Optional[str] = Field(None, description="Username from token")
    user_id: Optional[int] = Field(None, description="User ID from token")


class UserEmailVerification(BaseModel):
    """
    Schema for email verification.
    
    Attributes:
        token: Email verification token
    """
    
class UserEmailSignup(BaseModel):
    """
    Schema for email signup request.
    
    Attributes:
        email: User's email address
        password: User's password
        confirm_password: Password confirmation
        full_name: User's full name (optional)
    """
    
    email: EmailStr = Field(..., description="User's email address")
    password: str = Field(..., min_length=8, max_length=100, description="User's password")
    confirm_password: str = Field(..., description="Password confirmation")
    full_name: Optional[str] = Field(None, max_length=100, description="User's full name")
    
    @validator('email')
    def validate_email(cls, v):
        """Validate and normalize email."""
        return v.lower()
    
    @validator('password')
    def validate_password(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        
        if not re.search(r'[A-Za-z]', v):
            raise ValueError('Password must contain at least one letter')
        
        if not re.search(r'[0-9]', v):
            raise ValueError('Password must contain at least one digit')
        
        return v
    
    @validator('confirm_password')
    def validate_passwords_match(cls, v, values):
        """Validate that passwords match."""
        if 'password' in values and v != values['password']:
            raise ValueError('Passwords do not match')
        return v


class FirebaseAuthRequest(BaseModel):
    """
    Schema for Firebase authentication request.
    
    Attributes:
        id_token: Firebase ID token
    """
    
    id_token: str = Field(..., description="Firebase ID token")


class FirebaseUserResponse(UserResponse):
    """
    Schema for Firebase user response data.
    
    Attributes:
        firebase_uid: Firebase user UID
        auth_provider: Authentication provider (google, email, etc.)
    """
    
    firebase_uid: Optional[str] = Field(None, description="Firebase user UID")
    auth_provider: Optional[str] = Field(None, description="Authentication provider")


class FirebaseConfigResponse(BaseModel):
    """
    Schema for Firebase configuration response.
    
    Attributes:
        project_id: Firebase project ID
        auth_domain: Firebase auth domain
        api_key: Firebase web API key
        providers: Supported authentication providers
    """
    
    project_id: Optional[str] = Field(None, description="Firebase project ID")
    auth_domain: Optional[str] = Field(None, description="Firebase auth domain")
    api_key: Optional[str] = Field(None, description="Firebase web API key")
    providers: list[str] = Field(default=[], description="Supported authentication providers")
    token: str = Field(..., description="Email verification token")