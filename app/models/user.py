"""
User model for MongoDB using Beanie ODM.
"""

from datetime import datetime
from typing import Optional
from beanie import Document
from pydantic import Field


class User(Document):
    """
    User model for storing user information and authentication data.
    
    Attributes:
        email: User's email address (unique)
        username: User's username (unique)
        full_name: User's full name
        firebase_uid: Firebase user UID (unique)
        auth_provider: Authentication provider (email, google, etc.)
        is_active: Whether the user account is active
        is_superuser: Whether the user has admin privileges
        is_verified: Whether the user's email is verified
        bio: User's biography/description
        avatar_url: URL to user's avatar image
        last_login: Timestamp of last login
        created_at: User creation timestamp
        updated_at: Last update timestamp
    """
    
    # Basic Information
    email: str = Field(..., unique=True, index=True)
    username: str = Field(..., unique=True, index=True)
    full_name: Optional[str] = None
    
    # Firebase Authentication
    firebase_uid: Optional[str] = Field(None, unique=True, index=True)
    
    # AWS Cognito Authentication
    cognito_sub: Optional[str] = Field(None, unique=True, index=True)
    
    # Google OAuth Authentication
    google_sub: Optional[str] = Field(None, unique=True, index=True)
    
    auth_provider: str = Field(default="email")  # email, google, firebase, cognito, etc.
    
    # Status Fields
    is_active: bool = Field(default=True)
    is_superuser: bool = Field(default=False)
    is_verified: bool = Field(default=False)
    
    # Profile Information
    bio: Optional[str] = None
    avatar_url: Optional[str] = None
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_login: Optional[datetime] = None
    
    class Settings:
        """Beanie document settings."""
        name = "users"
        indexes = [
            "email",
            "username",
            "firebase_uid",
            "cognito_sub",
            "google_sub",
            "auth_provider"
        ]
    
    def __repr__(self) -> str:
        """String representation of the User model."""
        return f"<User(id={self.id}, email='{self.email}', username='{self.username}')>"
    
    def to_dict(self) -> dict:
        """Convert User instance to dictionary."""
        return {
            "id": str(self.id),
            "email": self.email,
            "username": self.username,
            "full_name": self.full_name,
            "firebase_uid": self.firebase_uid,
            "auth_provider": self.auth_provider,
            "is_active": self.is_active,
            "is_superuser": self.is_superuser,
            "is_verified": self.is_verified,
            "bio": self.bio,
            "avatar_url": self.avatar_url,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "last_login": self.last_login
        }
    
    @property
    def is_admin(self) -> bool:
        """Check if user has admin privileges."""
        return self.is_superuser
    
    @property
    def display_name(self) -> str:
        """Get display name (full name or username)."""
        return self.full_name or self.username
    
    def check_permissions(self, required_permission: str) -> bool:
        """
        Check if user has required permission.
        
        Args:
            required_permission: The permission to check for
            
        Returns:
            bool: True if user has permission
        """
        if not self.is_active:
            return False
        
        if required_permission == "admin":
            return self.is_superuser
        
        # For now, all active users have basic permissions
        return True
    
    def can_access_resource(self, resource_owner_id: str) -> bool:
        """
        Check if user can access a resource.
        
        Args:
            resource_owner_id: ID of the resource owner
            
        Returns:
            bool: True if user can access the resource
        """
        # Users can access their own resources or admins can access all
        return str(self.id) == resource_owner_id or self.is_superuser
    
    async def save_changes(self):
        """Save changes and update the updated_at timestamp."""
        self.updated_at = datetime.utcnow()
        await self.save()
    
    @classmethod
    async def find_by_email(cls, email: str) -> Optional["User"]:
        """Find user by email address."""
        return await cls.find_one(cls.email == email)
    
    @classmethod
    async def find_by_username(cls, username: str) -> Optional["User"]:
        """Find user by username."""
        return await cls.find_one(cls.username == username)
    
    @classmethod
    async def find_by_firebase_uid(cls, firebase_uid: str) -> Optional["User"]:
        """Find user by Firebase UID."""
        return await cls.find_one(cls.firebase_uid == firebase_uid)
    
    @classmethod
    async def find_by_google_sub(cls, google_sub: str) -> Optional["User"]:
        """Find user by Google sub (OAuth user ID)."""
        return await cls.find_one(cls.google_sub == google_sub)
    
    @classmethod
    async def find_by_email_or_username(cls, identifier: str) -> Optional["User"]:
        """Find user by email or username."""
        return await cls.find_one(
            {"$or": [{"email": identifier}, {"username": identifier}]}
        )