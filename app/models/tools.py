"""
Tool and ToolParameter models for agent capabilities.
"""

from enum import Enum
from typing import Optional, List, Dict, Any
from pydantic import Field
from app.models.base import BaseModel


class ToolType(str, Enum):
    """Enumeration of tool types"""
    FUNCTION = "function"
    API = "api"
    DATABASE = "database"
    WEB_SCRAPER = "web_scraper"
    FILE_PROCESSOR = "file_processor"
    CALCULATOR = "calculator"
    CODE_EXECUTOR = "code_executor"
    APIFY = "apify"
    BRAVE_SEARCH = "brave_search"
    FIRECRAWL = "firecrawl"
    GITHUB = "github"
    GMAIL = "gmail"
    GOOGLE_MAPS = "google_maps"


class ToolParameter(BaseModel):
    """Model representing a tool parameter"""
    class Settings:
        """Beanie document settings."""
        name = "tool_parameters"
        indexes = [
            "tool_id",
            "name"
        ]

    tool_id: str = Field(...)  # Reference to Tool.id
    name: str = Field(..., min_length=1, max_length=100)
    type: str = Field(...)
    description: Optional[str] = None
    required: bool = Field(default=False)
    default_value: Optional[Any] = None
    enum_values: Optional[List[str]] = None
    validation_pattern: Optional[str] = None


class Tool(BaseModel):
    """Model representing a tool that agents can use"""
    class Settings:
        """Beanie document settings."""
        name = "tools"
        indexes = [
            "name",
            "type",
            "agent_id",
            "user_id"
        ]

    name: str = Field(..., min_length=1, max_length=100)
    type: str = Field(...)  # ToolType as string
    description: str = Field(..., min_length=1, max_length=1000)
    version: str = Field(default="1.0.0")
    endpoint: Optional[str] = None
    authentication_required: bool = Field(default=False)
    return_type: Optional[str] = None
    timeout: int = Field(default=30)
    rate_limit: Optional[int] = None
    function_schema: Optional[Dict[str, Any]] = None
    sanitize_args: bool = Field(default=True)
    show_arguments: bool = Field(default=True)
    agent_id: Optional[str] = None  # Reference to Agent.id
    user_id: Optional[str] = None  # Reference to User.id
    tags: List[str] = Field(default_factory=list)
    is_active: bool = Field(default=True)
