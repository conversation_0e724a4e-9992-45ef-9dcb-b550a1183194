"""
AI Model representation for different types of models.
"""

from typing import Optional, List, Dict, Any
from pydantic import Field
from app.models.base import BaseModel


class AIModel(BaseModel):
    """Model representing an AI model"""
    class Settings:
        """Beanie document settings."""
        name = "ai_models"
        indexes = [
            "name",
            "provider_id",
            "model_type",
            "user_id"
        ]

    class Config:
        """Model configuration."""
        protected_namespaces = ()

    name: str = Field(..., min_length=1, max_length=100)
    provider_id: str = Field(...)  # Reference to Provider.id
    model_type: str = Field(...)  # ModelType as string
    user_id: Optional[str] = None  # Reference to User.id
    version: Optional[str] = None
    max_tokens: Optional[int] = None
    max_completion_tokens: Optional[int] = None
    context_window: Optional[int] = None
    input_cost_per_token: Optional[float] = None
    output_cost_per_token: Optional[float] = None
    supports_streaming: bool = Field(default=False)
    supports_function_calling: bool = Field(default=False)
    supports_structured_outputs: bool = Field(default=False)
    supports_json_schema: bool = Field(default=False)
    temperature: Optional[float] = Field(default=None, ge=0.0, le=2.0)
    top_p: Optional[float] = Field(default=None, ge=0.0, le=1.0)
    frequency_penalty: Optional[float] = Field(default=None, ge=-2.0, le=2.0)
    presence_penalty: Optional[float] = Field(default=None, ge=-2.0, le=2.0)
    reasoning_effort: Optional[str] = None
    capabilities: List[str] = Field(default_factory=list)
    parameters: Optional[Dict[str, Any]] = None
    is_active: bool = Field(default=True)
