"""
Base model class with common fields and functionality.
"""

from datetime import datetime
from typing import Optional
from beanie import Document
from pydantic import Field


class BaseModel(Document):
    """
    Base model class that provides common fields and functionality.
    
    All models should inherit from this class to get:
    - Created timestamp
    - Updated timestamp
    - Common methods
    """
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<{self.__class__.__name__}(id={self.id})>"
    
    def to_dict(self) -> dict:
        """Convert model instance to dictionary."""
        return {
            "id": str(self.id),
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            **{k: v for k, v in self.dict().items() if k not in ["id", "created_at", "updated_at"]}
        }
    
    async def save_changes(self):
        """Save changes and update the updated_at timestamp."""
        self.updated_at = datetime.utcnow()
        await self.save()