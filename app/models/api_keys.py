"""
API key model for managing provider authentication.
"""

from datetime import datetime
from typing import Optional, List
from pydantic import Field, SecretStr
from app.models.base import BaseModel


class ApiKey(BaseModel):
    """Model representing API keys for providers"""
    class Settings:
        """Beanie document settings."""
        name = "api_keys"
        indexes = [
            "name",
            "provider_id"
        ]

    name: str = Field(..., min_length=1, max_length=100)
    provider_id: str = Field(...)  # Reference to Provider.id
    key_value: SecretStr = Field(...)  # Encrypted in production
    is_active: bool = Field(default=True)
    usage_limit: Optional[int] = None
    current_usage: int = Field(default=0)
    expires_at: Optional[datetime] = None
    scopes: List[str] = Field(default_factory=list)
    last_used_at: Optional[datetime] = None
