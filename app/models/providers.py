"""
Provider model for AI service providers.
"""

from enum import Enum
from typing import Optional, List, Dict, Any
from pydantic import Field
from app.models.base import BaseModel


class ProviderType(str, Enum):
    """Enumeration of supported AI providers"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    AZURE = "azure"
    HUGGING_FACE = "huggingface"
    COHERE = "cohere"
    MISTRAL = "mistral"
    OLLAMA = "ollama"
    GROQ = "groq"
    PERPLEXITY = "perplexity"
    CEREBRAS = "cerebras"
    LITELLM = "litellm"


class ModelType(str, Enum):
    """Enumeration of model types"""
    CHAT = "chat"
    COMPLETION = "completion"
    EMBEDDING = "embedding"
    IMAGE_GENERATION = "image_generation"
    TEXT_TO_SPEECH = "text_to_speech"
    SPEECH_TO_TEXT = "speech_to_text"
    REASONING = "reasoning"


class Provider(BaseModel):
    """Model representing an AI provider"""
    class Settings:
        """Beanie document settings."""
        name = "providers"
        indexes = [
            "name",
            "type"
        ]

    name: str = Field(..., min_length=1, max_length=100)
    type: str = Field(...)  # ProviderType as string
    base_url: Optional[str] = None
    description: Optional[str] = None
    is_active: bool = Field(default=True)
    rate_limits: Optional[Dict[str, int]] = None
    supported_model_types: List[str] = Field(default_factory=list)  # ModelType as string array
    extra_headers: Optional[Dict[str, str]] = None
    request_params: Optional[Dict[str, Any]] = None
