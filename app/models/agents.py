"""
Agent and AgentConfig models for AI agents.
"""

from enum import Enum
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import Field
from app.models.base import BaseModel


class AgentStatus(str, Enum):
    """Enumeration of agent statuses"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    TRAINING = "training"
    MAINTENANCE = "maintenance"
    DEPRECATED = "deprecated"
    PAUSED = "paused"
    ERROR = "error"


class AgentConfig(BaseModel):
    """Configuration settings for an agent"""
    class Settings:
        """Beanie document settings."""
        name = "agent_configs"
        indexes = [
            "agent_id"
        ]

    agent_id: str = Field(...)  # Reference to Agent.id
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    max_tokens: Optional[int] = None
    max_completion_tokens: Optional[int] = None
    top_p: Optional[float] = Field(default=None, ge=0.0, le=1.0)
    frequency_penalty: Optional[float] = Field(default=None, ge=-2.0, le=2.0)
    presence_penalty: Optional[float] = Field(default=None, ge=-2.0, le=2.0)
    stop_sequences: List[str] = Field(default_factory=list)
    reasoning_effort: Optional[str] = None
    tool_choice: Optional[Dict[str, Any]] = None
    tool_call_limit: Optional[int] = None
    show_tool_calls: bool = Field(default=True)
    markdown: bool = Field(default=False)
    add_messages_to_memory: bool = Field(default=True)
    add_tool_execution_results_to_memory: bool = Field(default=True)
    num_messages_to_add_to_model: Optional[int] = None
    timeout: int = Field(default=60)
    retry_attempts: int = Field(default=3)
    custom_parameters: Optional[Dict[str, Any]] = None


class Agent(BaseModel):
    """Model representing an AI agent"""
    class Settings:
        """Beanie document settings."""
        name = "agents"
        indexes = [
            "name",
            "model_id",
            "user_id",
            "team_id",
            "app_id"
        ]

    class Config:
        """Model configuration."""
        protected_namespaces = ()

    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    system_prompt: Optional[str] = None
    instructions: List[str] = Field(default_factory=list)
    model_id: str = Field(...)  # Reference to AIModel.id
    fallback_model_ids: List[str] = Field(default_factory=list)  # Reference to AIModel.id
    tool_ids: List[str] = Field(default_factory=list)  # Reference to Tool.id
    session_id: Optional[str] = None
    user_id: Optional[str] = None
    team_id: Optional[str] = None
    app_id: Optional[str] = None
    workflow_id: Optional[str] = None
    status: str = Field(default="active")  # AgentStatus as string
    tags: List[str] = Field(default_factory=list)
    metadata: Optional[Dict[str, Any]] = None
    usage_stats: Dict[str, int] = Field(default_factory=dict)
    is_public: bool = Field(default=False)
    last_used_at: Optional[datetime] = None
