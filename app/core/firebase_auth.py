"""
Firebase Authentication service for handling Google and Email authentication.
"""

import json
import logging
from typing import Optional, Dict, Any
from datetime import datetime

import firebase_admin
from firebase_admin import credentials, auth
from fastapi import HTTPException, status
from pydantic import BaseModel

from app.core.config import settings

logger = logging.getLogger(__name__)


class FirebaseUser(BaseModel):
    """Firebase user data model."""
    uid: str
    email: Optional[str] = None
    email_verified: bool = False
    name: Optional[str] = None
    picture: Optional[str] = None
    provider: str
    firebase_claims: Dict[str, Any] = {}


class FirebaseAuthService:
    """Firebase Authentication service."""
    
    def __init__(self):
        """Initialize Firebase Admin SDK."""
        self._app = None
        self._initialize_firebase()
    
    def _initialize_firebase(self) -> None:
        """Initialize Firebase Admin SDK with service account credentials from environment variables."""
        try:
            # Check if Firebase is already initialized
            if firebase_admin._apps:
                self._app = firebase_admin.get_app()
                logger.info("Firebase Admin SDK already initialized")
                return
            
            # Initialize Firebase with environment variables
            if self._has_service_account_config():
                cred_dict = self._get_service_account_dict()
                cred = credentials.Certificate(cred_dict)
                self._app = firebase_admin.initialize_app(cred)
                logger.info("Firebase Admin SDK initialized with environment variables")
            else:
                logger.warning("Firebase configuration not found. Firebase auth will not be available.")
                logger.info("Please configure Firebase environment variables to enable authentication.")
                
        except Exception as e:
            logger.warning(f"Failed to initialize Firebase: {str(e)}")
            logger.info("Firebase authentication will not be available. Configure Firebase credentials to enable.")
            # Don't raise exception, just log the warning
    
    def _has_service_account_config(self) -> bool:
        """Check if all required Firebase service account configurations are available."""
        required_fields = [
            settings.FIREBASE_PROJECT_ID,
            settings.FIREBASE_PRIVATE_KEY_ID,
            settings.FIREBASE_CLIENT_EMAIL,
            settings.FIREBASE_CLIENT_ID,
        ]
        
        # Check if we have either the regular private key or base64 encoded private key
        has_private_key = bool(settings.FIREBASE_PRIVATE_KEY) or bool(settings.FIREBASE_PRIVATE_KEY_BASE64)
        
        return all(field for field in required_fields) and has_private_key
    
    def _get_service_account_dict(self) -> Dict[str, str]:
        """Get Firebase service account configuration as dictionary."""
        private_key = None
        
        # Try to get private key from base64 encoded version first
        if settings.FIREBASE_PRIVATE_KEY_BASE64:
            try:
                import base64
                private_key = base64.b64decode(settings.FIREBASE_PRIVATE_KEY_BASE64).decode('utf-8')
                logger.info("Using base64 encoded private key")
            except Exception as e:
                logger.warning(f"Failed to decode base64 private key: {str(e)}")
                
        # Fallback to regular private key
        if not private_key and settings.FIREBASE_PRIVATE_KEY:
            private_key = settings.FIREBASE_PRIVATE_KEY
            # Handle different possible formats of the private key
            if private_key.startswith('"') and private_key.endswith('"'):
                private_key = private_key[1:-1]  # Remove surrounding quotes
            private_key = private_key.replace('\\n', '\n')  # Replace literal \n with actual newlines
            logger.info("Using regular private key")
        
        return {
            "type": settings.FIREBASE_TYPE,
            "project_id": settings.FIREBASE_PROJECT_ID,
            "private_key_id": settings.FIREBASE_PRIVATE_KEY_ID,
            "private_key": private_key,
            "client_email": settings.FIREBASE_CLIENT_EMAIL,
            "client_id": settings.FIREBASE_CLIENT_ID,
            "auth_uri": settings.FIREBASE_AUTH_URI,
            "token_uri": settings.FIREBASE_TOKEN_URI,
            "auth_provider_x509_cert_url": settings.FIREBASE_AUTH_PROVIDER_X509_CERT_URL,
            "client_x509_cert_url": settings.FIREBASE_CLIENT_X509_CERT_URL,
            "universe_domain": settings.FIREBASE_UNIVERSE_DOMAIN,
        }
    
    async def verify_firebase_token(self, id_token: str, check_revoked: bool = False) -> FirebaseUser:
        """
        Verify Firebase ID token and return user information.
        
        Args:
            id_token: Firebase ID token from client
            check_revoked: Whether to check if the token has been revoked
            
        Returns:
            FirebaseUser: User information from Firebase
            
        Raises:
            HTTPException: If token is invalid or verification fails
        """
        try:
            if not self._app:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Firebase authentication service not available"
                )
            
            # Verify the ID token
            decoded_token = auth.verify_id_token(id_token, check_revoked=check_revoked)
            
            # Extract user information
            uid = decoded_token['uid']
            email = decoded_token.get('email')
            email_verified = decoded_token.get('email_verified', False)
            name = decoded_token.get('name')
            picture = decoded_token.get('picture')
            audience = decoded_token.get('aud')
            
            # Log the audience for debugging
            logger.info(f"Token audience (project_id): {audience}, Expected: {settings.FIREBASE_PROJECT_ID}")
            
            # Determine the provider
            provider = 'email'
            if 'firebase' in decoded_token:
                if 'sign_in_provider' in decoded_token['firebase']:
                    provider = decoded_token['firebase']['sign_in_provider']
                elif 'identities' in decoded_token['firebase']:
                    identities = decoded_token['firebase']['identities']
                    if 'google.com' in identities:
                        provider = 'google'
                    elif 'email' in identities:
                        provider = 'email'
            
            return FirebaseUser(
                uid=uid,
                email=email,
                email_verified=email_verified,
                name=name,
                picture=picture,
                provider=provider,
                firebase_claims=decoded_token
            )
            
        except auth.InvalidIdTokenError as e:
            logger.warning(f"Invalid Firebase ID token: {str(e)}")
            
            # Check if it's an audience mismatch
            if "incorrect \"aud\" (audience) claim" in str(e):
                logger.info("Attempting verification without audience check...")
                try:
                    # Try to verify without strict audience checking (for development)
                    import jwt as pyjwt
                    import requests
                    import json
                    
                    # Get Firebase public keys from the correct project
                    logger.info("Fetching Firebase public keys...")
                    
                    # First try the standard endpoint
                    response = requests.get('https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>', timeout=10)
                    response.raise_for_status()
                    public_keys = response.json()
                    
                    # Get the actual project ID from the token
                    import jwt as pyjwt_temp
                    unverified_payload = pyjwt_temp.decode(id_token, options={"verify_signature": False})
                    token_project_id = unverified_payload.get('aud')
                    
                    # If key not found and project differs, try project-specific endpoint
                    unverified_header = pyjwt.get_unverified_header(id_token)
                    key_id = unverified_header['kid']
                    
                    if key_id not in public_keys and token_project_id != settings.FIREBASE_PROJECT_ID:
                        logger.info(f"Key not found in standard keys, trying project-specific endpoint for {token_project_id}")
                        try:
                            # Try the project-specific endpoint
                            project_response = requests.get(f'https://securetoken.googleapis.com/v1/projects/{token_project_id}/publicKeys', timeout=10)
                            if project_response.status_code == 200:
                                project_keys = project_response.json()
                                public_keys.update(project_keys)
                                logger.info(f"Fetched project-specific keys for {token_project_id}")
                        except Exception as e:
                            logger.warning(f"Could not fetch project-specific keys: {e}")
                    
                    # Decode token header to get the key ID
                    unverified_header = pyjwt.get_unverified_header(id_token)
                    key_id = unverified_header['kid']
                    logger.info(f"Token key ID: {key_id}")
                    
                    if key_id in public_keys:
                        public_key = public_keys[key_id]
                        logger.info("Found matching public key, attempting decode...")
                        
                        # Convert the certificate to a public key
                        from cryptography import x509
                        from cryptography.hazmat.backends import default_backend
                        
                        cert = x509.load_pem_x509_certificate(public_key.encode(), default_backend())
                        public_key_obj = cert.public_key()
                        
                        decoded_token = pyjwt.decode(
                            id_token,
                            public_key_obj,
                            algorithms=['RS256'],
                            options={
                                "verify_aud": False,  # Skip audience verification
                                "verify_exp": True,   # Keep expiration check
                                "verify_iat": True,   # Keep issued at check
                                "verify_nbf": True,   # Keep not before check
                            }
                        )
                        
                        # Extract user information
                        uid = decoded_token['uid']
                        email = decoded_token.get('email')
                        email_verified = decoded_token.get('email_verified', False)
                        name = decoded_token.get('name')
                        picture = decoded_token.get('picture')
                        
                        # Determine the provider
                        provider = 'email'
                        if 'firebase' in decoded_token:
                            if 'sign_in_provider' in decoded_token['firebase']:
                                provider = decoded_token['firebase']['sign_in_provider']
                            elif 'identities' in decoded_token['firebase']:
                                identities = decoded_token['firebase']['identities']
                                if 'google.com' in identities:
                                    provider = 'google'
                                elif 'email' in identities:
                                    provider = 'email'
                        
                        logger.info(f"✅ Token verified with flexible audience check for project: {decoded_token.get('aud')}")
                        
                        return FirebaseUser(
                            uid=uid,
                            email=email,
                            email_verified=email_verified,
                            name=name,
                            picture=picture,
                            provider=provider,
                            firebase_claims=decoded_token
                        )
                    else:
                        logger.warning(f"Key ID {key_id} not found in public keys")
                        
                except Exception as fallback_error:
                    logger.warning(f"Fallback verification failed: {str(fallback_error)}")
                    import traceback
                    logger.debug(f"Fallback error traceback: {traceback.format_exc()}")
            
            # If fallback fails, re-raise the original exception
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication token",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except auth.ExpiredIdTokenError as e:
            logger.warning(f"Expired Firebase ID token: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication token has expired",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except Exception as e:
            logger.error(f"Firebase token verification failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication verification failed",
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    async def get_firebase_user(self, uid: str) -> Optional[FirebaseUser]:
        """
        Get Firebase user by UID.
        
        Args:
            uid: Firebase user UID
            
        Returns:
            Optional[FirebaseUser]: User information if found
        """
        try:
            if not self._app:
                return None
            
            user_record = auth.get_user(uid)
            
            # Determine provider from user metadata
            provider = 'email'
            if user_record.provider_data:
                for provider_data in user_record.provider_data:
                    if provider_data.provider_id == 'google.com':
                        provider = 'google'
                        break
            
            return FirebaseUser(
                uid=user_record.uid,
                email=user_record.email,
                email_verified=user_record.email_verified,
                name=user_record.display_name,
                picture=user_record.photo_url,
                provider=provider,
                firebase_claims={}
            )
            
        except auth.UserNotFoundError:
            return None
        except Exception as e:
            logger.error(f"Failed to get Firebase user {uid}: {str(e)}")
            return None
    
    async def create_custom_token(self, uid: str, additional_claims: Optional[Dict[str, Any]] = None) -> str:
        """
        Create a custom Firebase token for a user.
        
        Args:
            uid: User UID
            additional_claims: Additional claims to include in token
            
        Returns:
            str: Custom Firebase token
        """
        try:
            if not self._app:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Firebase authentication service not available"
                )
            
            return auth.create_custom_token(uid, additional_claims)
            
        except Exception as e:
            logger.error(f"Failed to create custom token for user {uid}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create authentication token"
            )
    
    async def create_firebase_user_with_provider(self, email: str, google_id: Optional[str] = None,
                                               password: Optional[str] = None,
                                               display_name: Optional[str] = None,
                                               photo_url: Optional[str] = None,
                                               email_verified: bool = False,
                                               provider: str = "email") -> str:
        """
        Create a new user in Firebase with proper provider data.
        
        Args:
            email: User's email address
            google_id: Google user ID (for Google OAuth users)
            password: User's password (optional for OAuth users)
            display_name: User's display name
            photo_url: User's profile photo URL
            email_verified: Whether email is verified
            provider: Authentication provider (email, google, etc.)
            
        Returns:
            str: Firebase UID of created user
            
        Raises:
            HTTPException: If user creation fails
        """
        try:
            if not self._app:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Firebase authentication service not available"
                )
            
            # Check if user already exists by email
            try:
                existing_user = auth.get_user_by_email(email)
                logger.info(f"Firebase user already exists: {existing_user.uid}")
                return existing_user.uid
            except auth.UserNotFoundError:
                pass  # User doesn't exist, continue with creation
            
            # For Google OAuth users, try using import to set provider data properly
            if provider == "google" and google_id and not password:
                try:
                    from firebase_admin import auth as firebase_auth_import
                    
                    # Generate a Firebase-compatible UID from Google ID
                    firebase_uid = f"google_{google_id}" if not google_id.startswith("google_") else google_id
                    
                    # Check if this UID already exists
                    try:
                        existing_by_uid = auth.get_user(firebase_uid)
                        logger.info(f"Firebase user with UID already exists: {firebase_uid}")
                        return firebase_uid
                    except auth.UserNotFoundError:
                        pass
                    
                    # Prepare user import data with Google provider
                    user_import = firebase_auth_import.ImportUserRecord(
                        uid=firebase_uid,
                        email=email,
                        email_verified=email_verified,
                        display_name=display_name,
                        photo_url=photo_url,
                        provider_data=[
                            firebase_auth_import.UserProvider(
                                uid=google_id,
                                email=email,
                                display_name=display_name,
                                photo_url=photo_url,
                                provider_id="google.com"
                            )
                        ]
                    )
                    
                    # Import user with provider data
                    result = auth.import_users([user_import])
                    if result.success_count > 0:
                        logger.info(f"Imported Firebase user with Google provider: {firebase_uid}")
                        return firebase_uid
                    else:
                        logger.error(f"Failed to import Google user: {result.errors}")
                        
                except Exception as import_error:
                    logger.warning(f"Failed to import user with provider data: {str(import_error)}")
                    # Continue to fallback creation
            
            # Regular user creation (for email users or if import failed)
            user_properties = {
                'email': email,
                'email_verified': email_verified,
            }
            
            if password:
                user_properties['password'] = password
                
            if display_name:
                user_properties['display_name'] = display_name
                
            if photo_url:
                user_properties['photo_url'] = photo_url
            
            # Create user in Firebase
            user_record = auth.create_user(**user_properties)
            logger.info(f"Created Firebase user with UID: {user_record.uid} (provider: {provider})")
            return user_record.uid
            
        except auth.EmailAlreadyExistsError:
            logger.warning(f"Firebase user with email {email} already exists")
            try:
                existing_user = auth.get_user_by_email(email)
                return existing_user.uid
            except Exception as e:
                logger.error(f"Failed to get existing Firebase user: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="User with this email already exists but cannot be retrieved"
                )
        except Exception as e:
            logger.error(f"Failed to create Firebase user {email}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create Firebase user: {str(e)}"
            )

    async def create_firebase_user(self, email: str, password: Optional[str] = None,
                                 display_name: Optional[str] = None,
                                 photo_url: Optional[str] = None,
                                 email_verified: bool = False,
                                 provider: str = "email") -> str:
        """Legacy method - calls new method with provider data."""
        return await self.create_firebase_user_with_provider(
            email=email,
            password=password,
            display_name=display_name,
            photo_url=photo_url,
            email_verified=email_verified,
            provider=provider
        )
    
    async def update_firebase_user(self, uid: str, email: Optional[str] = None,
                                 display_name: Optional[str] = None,
                                 photo_url: Optional[str] = None,
                                 email_verified: Optional[bool] = None) -> bool:
        """
        Update an existing Firebase user.
        
        Args:
            uid: Firebase user UID
            email: New email address
            display_name: New display name
            photo_url: New photo URL
            email_verified: New email verification status
            
        Returns:
            bool: True if update successful
        """
        try:
            if not self._app:
                return False
            
            # Prepare update properties
            update_properties = {}
            
            if email is not None:
                update_properties['email'] = email
                
            if display_name is not None:
                update_properties['display_name'] = display_name
                
            if photo_url is not None:
                update_properties['photo_url'] = photo_url
                
            if email_verified is not None:
                update_properties['email_verified'] = email_verified
            
            # Update user in Firebase
            auth.update_user(uid, **update_properties)
            
            logger.info(f"Updated Firebase user with UID: {uid}")
            return True
            
        except auth.UserNotFoundError:
            logger.warning(f"Firebase user with UID {uid} not found for update")
            return False
        except Exception as e:
            logger.error(f"Failed to update Firebase user {uid}: {str(e)}")
            return False
    
    def is_available(self) -> bool:
        """Check if Firebase authentication service is available."""
        return self._app is not None


# Global Firebase auth service instance
firebase_auth = FirebaseAuthService()