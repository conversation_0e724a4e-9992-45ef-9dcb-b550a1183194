"""
Google OAuth Authentication service for handling Google ID tokens.
"""

import json
import logging
from typing import Optional, Dict, Any
from datetime import datetime

import jwt
import requests
from fastapi import HTTPEx<PERSON>, status
from pydantic import BaseModel

from app.core.config import settings

logger = logging.getLogger(__name__)


class GoogleUser(BaseModel):
    """Google user data model."""
    sub: str
    email: Optional[str] = None
    email_verified: bool = False
    name: Optional[str] = None
    picture: Optional[str] = None
    given_name: Optional[str] = None
    family_name: Optional[str] = None
    locale: Optional[str] = None
    hd: Optional[str] = None  # Hosted domain (G Suite)


class GoogleAuthService:
    """Google OAuth Authentication service."""
    
    def __init__(self):
        """Initialize Google OAuth service."""
        self._public_keys_cache = None
        self._cache_expiry = None
    
    async def verify_google_token(self, id_token: str) -> GoogleUser:
        """
        Verify Google OAuth ID token and return user information.
        
        Args:
            id_token: Google OAuth ID token from client
            
        Returns:
            GoogleUser: User information from Google
            
        Raises:
            HTTPException: If token is invalid or verification fails
        """
        try:
            # Get Google's public keys
            public_keys = await self._get_google_public_keys()
            
            # Decode token header to get the key ID
            unverified_header = jwt.get_unverified_header(id_token)
            key_id = unverified_header.get('kid')
            
            if not key_id or key_id not in public_keys:
                logger.warning(f"Key ID {key_id} not found in Google public keys")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token key ID",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            # Get the public key
            public_key = public_keys[key_id]
            
            # Convert the certificate to a public key
            from cryptography import x509
            from cryptography.hazmat.backends import default_backend
            
            cert = x509.load_pem_x509_certificate(public_key.encode(), default_backend())
            public_key_obj = cert.public_key()
            
            # Verify and decode the token
            decoded_token = jwt.decode(
                id_token,
                public_key_obj,
                algorithms=['RS256'],
                audience=None,  # We'll verify audience separately
                options={
                    "verify_aud": False,  # We handle audience verification below
                    "verify_exp": True,
                    "verify_iat": True,
                    "verify_nbf": True,
                }
            )
            
            # Verify issuer
            if decoded_token.get('iss') not in ['https://accounts.google.com', 'accounts.google.com']:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token issuer",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            # Extract user information
            sub = decoded_token['sub']
            email = decoded_token.get('email')
            email_verified = decoded_token.get('email_verified', False)
            name = decoded_token.get('name')
            picture = decoded_token.get('picture')
            given_name = decoded_token.get('given_name')
            family_name = decoded_token.get('family_name')
            locale = decoded_token.get('locale')
            hd = decoded_token.get('hd')  # Hosted domain
            
            logger.info(f"✅ Google token verified successfully for user: {email}")
            
            return GoogleUser(
                sub=sub,
                email=email,
                email_verified=email_verified,
                name=name,
                picture=picture,
                given_name=given_name,
                family_name=family_name,
                locale=locale,
                hd=hd
            )
            
        except jwt.ExpiredSignatureError:
            logger.warning("Expired Google ID token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication token has expired",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid Google ID token: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication token",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except Exception as e:
            logger.error(f"Google token verification failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication verification failed",
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    async def _get_google_public_keys(self) -> Dict[str, str]:
        """
        Get Google's public keys for token verification.
        
        Returns:
            Dict[str, str]: Mapping of key IDs to public keys
        """
        try:
            # Use cached keys if available and not expired
            current_time = datetime.utcnow().timestamp()
            if (self._public_keys_cache and 
                self._cache_expiry and 
                current_time < self._cache_expiry):
                return self._public_keys_cache
            
            # Fetch fresh keys from Google
            response = requests.get(
                'https://www.googleapis.com/oauth2/v1/certs',
                timeout=10
            )
            response.raise_for_status()
            
            public_keys = response.json()
            
            # Cache the keys for 1 hour
            self._public_keys_cache = public_keys
            self._cache_expiry = current_time + 3600  # 1 hour
            
            logger.info(f"Fetched {len(public_keys)} Google public keys")
            return public_keys
            
        except Exception as e:
            logger.error(f"Failed to fetch Google public keys: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Unable to verify token - service temporarily unavailable"
            )
    
    def is_available(self) -> bool:
        """Check if Google OAuth authentication service is available."""
        return True  # Google OAuth is always available


# Global Google auth service instance
google_auth = GoogleAuthService() 