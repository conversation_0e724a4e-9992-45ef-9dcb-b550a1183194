"""
AWS Cognito Authentication service for handling Cognito ID tokens.
"""

import logging
import requests
from typing import Optional, Dict, Any
from datetime import datetime
import jwt
from fastapi import HTTPEx<PERSON>, status
from pydantic import BaseModel
from cryptography import x509
from cryptography.hazmat.backends import default_backend

from app.core.config import settings

logger = logging.getLogger(__name__)


class CognitoUser(BaseModel):
    """Cognito user data model."""
    sub: str  # Cognito user ID
    email: Optional[str] = None
    email_verified: bool = False
    username: str
    name: Optional[str] = None
    custom_attributes: Dict[str, Any] = {}
    token_use: str = "id"
    cognito_username: Optional[str] = None


class CognitoAuthService:
    """AWS Cognito Authentication service."""
    
    def __init__(self):
        """Initialize Cognito authentication service."""
        self._jwks_cache = {}
        self._region = "us-east-1"  # From your token issuer
        self._user_pool_id = "us-east-1_RXvmCJGiT"  # From your token issuer
        
    def _get_jwks_uri(self) -> str:
        """Get the JWKS URI for the Cognito User Pool."""
        return f"https://cognito-idp.{self._region}.amazonaws.com/{self._user_pool_id}/.well-known/jwks.json"
    
    def _get_cognito_public_keys(self) -> Dict[str, Any]:
        """Get public keys from Cognito JWKS endpoint."""
        jwks_uri = self._get_jwks_uri()
        
        try:
            response = requests.get(jwks_uri, timeout=10)
            response.raise_for_status()
            jwks = response.json()
            
            # Convert JWKS to a more usable format
            public_keys = {}
            for key in jwks.get('keys', []):
                kid = key.get('kid')
                if kid:
                    public_keys[kid] = key
            
            logger.info(f"Fetched {len(public_keys)} public keys from Cognito")
            return public_keys
            
        except Exception as e:
            logger.error(f"Failed to fetch Cognito public keys: {str(e)}")
            return {}
    
    def _jwk_to_pem(self, jwk: Dict[str, Any]) -> str:
        """Convert JWK to PEM format."""
        try:
            # Extract the x5c (certificate chain) if available
            if 'x5c' in jwk and jwk['x5c']:
                # Use the first certificate in the chain
                cert_str = jwk['x5c'][0]
                # Add PEM headers
                pem_cert = f"-----BEGIN CERTIFICATE-----\n{cert_str}\n-----END CERTIFICATE-----"
                return pem_cert
            else:
                # For RSA keys, we'd need to construct from n and e
                # This is more complex, so for now we'll rely on x5c
                raise ValueError("JWK does not contain x5c certificate chain")
                
        except Exception as e:
            logger.error(f"Failed to convert JWK to PEM: {str(e)}")
            raise
    
    async def verify_cognito_token(self, id_token: str) -> CognitoUser:
        """
        Verify AWS Cognito ID token and return user information.
        
        Args:
            id_token: Cognito ID token from client
            
        Returns:
            CognitoUser: User information from Cognito
            
        Raises:
            HTTPException: If token is invalid or verification fails
        """
        try:
            # Get the token header to find the key ID
            unverified_header = jwt.get_unverified_header(id_token)
            key_id = unverified_header.get('kid')
            
            if not key_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token missing key ID"
                )
            
            # Get public keys from Cognito
            public_keys = self._get_cognito_public_keys()
            
            if key_id not in public_keys:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail=f"Public key not found for key ID: {key_id}"
                )
            
            # Get the public key for this token
            jwk = public_keys[key_id]
            
            # Convert JWK to PEM format
            pem_key = self._jwk_to_pem(jwk)
            
            # Load the certificate and extract public key
            cert = x509.load_pem_x509_certificate(pem_key.encode(), default_backend())
            public_key = cert.public_key()
            
            # Verify the token
            decoded_token = jwt.decode(
                id_token,
                public_key,
                algorithms=['RS256'],
                options={
                    "verify_exp": True,
                    "verify_iat": True,
                    "verify_nbf": True,
                    "verify_aud": True,  # Verify audience
                    "verify_iss": True,  # Verify issuer
                }
            )
            
            # Validate issuer
            expected_issuer = f"https://cognito-idp.{self._region}.amazonaws.com/{self._user_pool_id}"
            if decoded_token.get('iss') != expected_issuer:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token issuer"
                )
            
            # Validate token use
            if decoded_token.get('token_use') != 'id':
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token is not an ID token"
                )
            
            # Extract user information
            sub = decoded_token['sub']
            email = decoded_token.get('email')
            email_verified = decoded_token.get('email_verified', False)
            cognito_username = decoded_token.get('cognito:username')
            
            # Extract custom attributes
            custom_attributes = {}
            for key, value in decoded_token.items():
                if key.startswith('custom:'):
                    custom_attributes[key] = value
            
            # Use custom:Name if available, otherwise use cognito:username or email
            name = custom_attributes.get('custom:Name') or cognito_username or email
            username = cognito_username or email or sub
            
            logger.info(f"✅ Cognito token verified successfully for user: {email}")
            
            return CognitoUser(
                sub=sub,
                email=email,
                email_verified=email_verified,
                username=username,
                name=name,
                custom_attributes=custom_attributes,
                token_use=decoded_token.get('token_use', 'id'),
                cognito_username=cognito_username
            )
            
        except jwt.ExpiredSignatureError:
            logger.warning("Cognito token has expired")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired"
            )
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid Cognito token: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Cognito token verification failed: {str(e)}")
            import traceback
            logger.debug(f"Full traceback: {traceback.format_exc()}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication verification failed"
            )
    
    def is_available(self) -> bool:
        """Check if Cognito authentication service is available."""
        return True  # Cognito is always available if configured


# Global Cognito auth service instance
cognito_auth = CognitoAuthService()