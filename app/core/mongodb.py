"""
MongoDB database configuration using Motor and Beanie.
"""

import logging
from typing import Optional
from motor.motor_asyncio import AsyncIOMotorClient
from beanie import init_beanie

from app.core.config import settings
from app.models.user import User

logger = logging.getLogger(__name__)

# Global MongoDB client
mongodb_client: Optional[AsyncIOMotorClient] = None


async def connect_to_mongo():
    """Create database connection."""
    global mongodb_client
    
    try:
        logger.info(f"Connecting to MongoDB: {settings.MONGODB_URL[:50]}...")
        mongodb_client = AsyncIOMotorClient(settings.MONGODB_URL)
        
        # Test the connection
        await mongodb_client.admin.command('ping')
        logger.info("✅ Successfully connected to MongoDB")
        
        # Initialize Beanie with User model
        database = mongodb_client[settings.MONGODB_DATABASE]
        await init_beanie(database=database, document_models=[User])
        logger.info("✅ Beanie ODM initialized successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to connect to MongoDB: {e}")
        logger.error(f"MongoDB URL preview: {settings.MONGODB_URL[:50]}...")
        # Re-raise the exception so Lambda handler can catch it
        raise Exception(f"MongoDB initialization failed: {str(e)}")


async def close_mongo_connection():
    """Close database connection."""
    global mongodb_client
    if mongodb_client:
        mongodb_client.close()
        logger.info("MongoDB connection closed")


def get_database():
    """Get database instance."""
    if mongodb_client is None:
        logger.warning("MongoDB client not initialized. Database operations will not be available.")
        return None
    return mongodb_client[settings.MONGODB_DATABASE]


def is_mongodb_initialized() -> bool:
    """Check if MongoDB and Beanie are properly initialized."""
    global mongodb_client
    if mongodb_client is None:
        return False
    
    try:
        # Check if Beanie is initialized by trying to access User settings
        from app.models.user import User
        User.get_settings()
        return True
    except Exception:
        return False


async def ensure_mongodb_initialized():
    """Ensure MongoDB is initialized, initialize if not."""
    global mongodb_client
    
    if not is_mongodb_initialized():
        logger.info("MongoDB not initialized, initializing now...")
        await connect_to_mongo()
    else:
        logger.debug("MongoDB already initialized")