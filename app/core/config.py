"""
Application configuration settings using Pydantic Settings with environment variables only.
"""

import os
from datetime import datetime
from typing import List, Optional, Union
from pydantic import validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings configuration."""
    
    # Environment Configuration
    STAGE: str = "dev"
    AWS_ACCESS_KEY_ID: Optional[str] = None
    AWS_SECRET_ACCESS_KEY: Optional[str] = None
    AWS_REGION: str = "us-east-1"
    
    # Project Information
    PROJECT_NAME: str = "Botmani Backend API"
    PROJECT_DESCRIPTION: str = "FastAPI backend service for Botmani application"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    
    # Server Configuration
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = True
    
    # Security Configuration
    SECRET_KEY: str = "your-secret-key-change-this-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 10080  # 7 days (7 * 24 * 60)
    
    # MongoDB Configuration
    MONGODB_URL: str = "mongodb://localhost:27017"
    MONGODB_DATABASE: str = "botmani_db"
    MONGODB_MIN_CONNECTIONS: int = 10
    MONGODB_MAX_CONNECTIONS: int = 20
    
    # CORS Configuration
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:8080",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:3001",
        "http://127.0.0.1:8080",
        "https://dev.botmani.com",
        "https://staging.botmani.com",
        "https://botmani.com"
    ]
    
    # Trusted Hosts
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # Rate Limiting
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_WINDOW: int = 60  # seconds
    
    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Email Configuration (for password reset, etc.)
    SMTP_TLS: bool = True
    SMTP_PORT: Optional[int] = None
    SMTP_HOST: Optional[str] = None
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAILS_FROM_EMAIL: Optional[str] = None
    EMAILS_FROM_NAME: Optional[str] = None
    
    # Firebase Configuration
    FIREBASE_TYPE: str = "service_account"
    FIREBASE_PROJECT_ID: Optional[str] = None
    FIREBASE_PRIVATE_KEY_ID: Optional[str] = None
    FIREBASE_PRIVATE_KEY: Optional[str] = None
    FIREBASE_PRIVATE_KEY_BASE64: Optional[str] = "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"
    FIREBASE_CLIENT_EMAIL: Optional[str] = None
    FIREBASE_CLIENT_ID: Optional[str] = None
    FIREBASE_AUTH_URI: str = "https://accounts.google.com/o/oauth2/auth"
    FIREBASE_TOKEN_URI: str = "https://oauth2.googleapis.com/token"
    FIREBASE_AUTH_PROVIDER_X509_CERT_URL: str = "https://www.googleapis.com/oauth2/v1/certs"
    FIREBASE_CLIENT_X509_CERT_URL: Optional[str] = None
    FIREBASE_UNIVERSE_DOMAIN: str = "googleapis.com"
    FIREBASE_WEB_API_KEY: Optional[str] = None
    
    # Google OAuth Configuration
    GOOGLE_CLIENT_ID: Optional[str] = None
    GOOGLE_CLIENT_SECRET: Optional[str] = None
    GOOGLE_REDIRECT_URI: Optional[str] = None
    
    # File Upload Configuration
    MAX_UPLOAD_SIZE: int = 10 * 1024 * 1024  # 10MB
    UPLOAD_DIRECTORY: str = "uploads"
    ALLOWED_EXTENSIONS: List[str] = [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx"]
    
    # Provider API Keys
    OPENAI_API_KEY: Optional[str] = None
    ANTHROPIC_API_KEY: Optional[str] = None
    GOOGLE_API_KEY: Optional[str] = None
    GROQ_API_KEY: Optional[str] = None
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> List[str]:
        """Parse CORS origins from string or list."""
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, list):
            return v
        raise ValueError(v)
    
    @validator("ALLOWED_EXTENSIONS", pre=True)
    def assemble_allowed_extensions(cls, v: Union[str, List[str]]) -> List[str]:
        """Parse allowed extensions from string or list."""
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, list):
            return v
        raise ValueError(v)
    
    @validator("ALLOWED_HOSTS", pre=True)
    def assemble_allowed_hosts(cls, v: Union[str, List[str]]) -> List[str]:
        """Parse allowed hosts from string or list."""
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, list):
            return v
        raise ValueError(v)
    
    @validator("DEBUG", pre=True)
    def assemble_debug(cls, v: Union[str, bool]) -> bool:
        """Parse DEBUG from string or bool."""
        if isinstance(v, str):
            return v.lower() in ("true", "1", "yes", "on")
        return v
    
    @validator("PORT", pre=True)
    def assemble_port(cls, v: Union[str, int]) -> int:
        """Parse PORT from string or int."""
        if isinstance(v, str):
            return int(v)
        return v
    
    @validator("ACCESS_TOKEN_EXPIRE_MINUTES", pre=True)
    def assemble_access_token_expire_minutes(cls, v: Union[str, int]) -> int:
        """Parse ACCESS_TOKEN_EXPIRE_MINUTES from string or int."""
        if isinstance(v, str):
            return int(v)
        return v
    
    @validator("MONGODB_MIN_CONNECTIONS", pre=True)
    def assemble_mongodb_min_connections(cls, v: Union[str, int]) -> int:
        """Parse MONGODB_MIN_CONNECTIONS from string or int."""
        if isinstance(v, str):
            return int(v)
        return v
    
    @validator("MONGODB_MAX_CONNECTIONS", pre=True)
    def assemble_mongodb_max_connections(cls, v: Union[str, int]) -> int:
        """Parse MONGODB_MAX_CONNECTIONS from string or int."""
        if isinstance(v, str):
            return int(v)
        return v
    
    @validator("RATE_LIMIT_REQUESTS", pre=True)
    def assemble_rate_limit_requests(cls, v: Union[str, int]) -> int:
        """Parse RATE_LIMIT_REQUESTS from string or int."""
        if isinstance(v, str):
            return int(v)
        return v
    
    @validator("RATE_LIMIT_WINDOW", pre=True)
    def assemble_rate_limit_window(cls, v: Union[str, int]) -> int:
        """Parse RATE_LIMIT_WINDOW from string or int."""
        if isinstance(v, str):
            return int(v)
        return v
    
    @validator("MAX_UPLOAD_SIZE", pre=True)
    def assemble_max_upload_size(cls, v: Union[str, int]) -> int:
        """Parse MAX_UPLOAD_SIZE from string or int."""
        if isinstance(v, str):
            return int(v)
        return v
    
    @validator("SMTP_TLS", pre=True)
    def assemble_smtp_tls(cls, v: Union[str, bool]) -> bool:
        """Parse SMTP_TLS from string or bool."""
        if isinstance(v, str):
            return v.lower() in ("true", "1", "yes", "on")
        return v
    
    @validator("SMTP_PORT", pre=True)
    def assemble_smtp_port(cls, v: Union[str, int, None]) -> Optional[int]:
        """Parse SMTP_PORT from string or int."""
        if v is None or v == "":
            return None
        if isinstance(v, str):
            return int(v) if v.isdigit() else None
        return v
    
    @staticmethod
    def get_current_timestamp() -> str:
        """Get current timestamp in ISO format."""
        return datetime.utcnow().isoformat() + "Z"
    
    def create_upload_directory(self) -> None:
        """Create upload directory if it doesn't exist."""
        if not os.path.exists(self.UPLOAD_DIRECTORY):
            os.makedirs(self.UPLOAD_DIRECTORY)
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()
