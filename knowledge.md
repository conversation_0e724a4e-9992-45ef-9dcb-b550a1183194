# Botmani Backend - FastAPI Project Knowledge Base

## Project Overview
- **Mission**: Backend API service for Botmani application
- **Technology Stack**: FastAPI, MongoDB, Pydantic, Firebase Authentication
- **Architecture**: RESTful API with Firebase authentication and MongoDB storage
- **Current Version**: 1.0.0
- **Target Audience**: API consumers, frontend applications

## Project Structure
```
botmani-backend/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI app instance
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py          # Configuration settings
│   │   ├── mongodb.py         # MongoDB connection
│   │   ├── firebase_auth.py   # Firebase authentication service
│   │   └── security.py        # JWT security utilities
│   ├── models/
│   │   ├── __init__.py
│   │   ├── user.py            # User model (Beanie ODM)
│   │   └── base.py            # Base model class
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── user.py            # User Pydantic schemas
│   │   └── base.py            # Base schema classes
│   ├── routers/
│   │   ├── __init__.py
│   │   ├── auth.py            # Authentication endpoints
│   │   └── health.py          # Health check endpoints
│   ├── utils/
│   │   ├── __init__.py
│   │   └── helpers.py         # Utility functions
│   └── dependencies/
│       ├── __init__.py
│       └── auth.py            # Authentication dependencies
├── docs/
│   └── firebase_setup.md      # Firebase setup guide
├── main.py                    # Application entry point
├── requirements.txt           # Python dependencies
├── .env                       # Environment variables
├── README.md                  # Project documentation
└── knowledge.md               # This file
```

## Technical Decisions

### Framework & Libraries
- **FastAPI**: Modern, fast web framework with automatic OpenAPI documentation
- **Motor**: Asynchronous MongoDB driver for Python
- **Beanie**: Async MongoDB ODM built on Pydantic
- **Pydantic**: Data validation and serialization
- **Uvicorn**: ASGI server for production
- **python-jose**: JWT token handling
- **passlib**: Password hashing
- **python-multipart**: File upload support
- **firebase-admin**: Firebase Admin SDK for server-side authentication
- **google-auth**: Google authentication libraries
- **google-auth-oauthlib**: OAuth2 flow for Google authentication

### Database
- **MongoDB**: NoSQL document database
- **Motor**: Async MongoDB driver
- **Beanie ODM**: Document modeling and validation
- **Automatic Indexing**: Database indexes created automatically

### Authentication
- **JWT**: JSON Web Tokens for stateless authentication
- **bcrypt**: Password hashing algorithm
- **OAuth2**: Standard authentication flow
- **Firebase Authentication**: Support for Google and Email providers
- **Firebase Admin SDK**: Server-side Firebase token verification
- **Flexible Authentication**: Support for both JWT and Firebase tokens

## API Patterns

### Response Structure
```json
{
  "success": true,
  "message": "Operation successful",
  "data": {},
  "meta": {
    "timestamp": "2025-01-07T20:50:44Z",
    "version": "1.0.0"
  }
}
```

### Error Response Structure
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Validation failed",
    "details": []
  },
  "meta": {
    "timestamp": "2025-01-07T20:50:44Z",
    "version": "1.0.0"
  }
}
```

### Endpoint Naming Conventions
- Use plural nouns for resources: `/users`, `/products`
- Use HTTP methods appropriately: GET, POST, PUT, DELETE
- Use consistent path parameters: `/users/{user_id}`
- Use query parameters for filtering: `/users?status=active`

## Core Features

### Authentication Only
- Firebase authentication with Google and Email providers
- Automatic user creation/linking for Firebase users
- User profile synchronization from Firebase data
- JWT token generation for API access
- Flexible authentication supporting both JWT and Firebase tokens
- No traditional email/password registration (Firebase only)

### Health Monitoring
- Health check endpoints
- Database connectivity checks
- Service status monitoring

## Data Models

### User Model (MongoDB/Beanie)
```python
class User(Document):
    _id: ObjectId (MongoDB Primary Key)
    email: str (Unique, Index)
    username: str (Unique, Index)
    full_name: str (Optional)
    
    # Firebase Authentication
    firebase_uid: str (Unique, Index, Optional)
    auth_provider: str (google, email, etc.)
    
    # Status Fields
    is_active: bool (default: True)
    is_superuser: bool (default: False)
    is_verified: bool (default: False)
    
    # Profile Information
    bio: str (Optional)
    avatar_url: str (Optional)
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    last_login: datetime (Optional)
```

## Component Standards

### Route Handlers
- Use dependency injection for common dependencies
- Implement proper error handling
- Return consistent response formats
- Include proper HTTP status codes

### Models
- Use SQLAlchemy declarative base
- Include created_at and updated_at timestamps
- Use proper relationships and constraints
- Include __repr__ methods for debugging

### Schemas
- Separate schemas for create, read, update operations
- Use Pydantic validators for data validation
- Include proper field documentation
- Use aliases for external API compatibility

## Security Patterns
- All endpoints require authentication unless explicitly public
- Use rate limiting for API endpoints
- Implement CORS for frontend integration
- Validate all input data using Pydantic schemas
- Use environment variables for sensitive configuration

## Development Standards
- Use type hints throughout the codebase
- Follow PEP 8 style guidelines
- Include docstrings for all functions and classes
- Use async/await for database operations
- Implement proper logging throughout the application

## Firebase Authentication

### Authentication Providers
- **Google**: OAuth2 authentication through Firebase and direct Google OAuth
- **Email/Password**: Both Firebase and traditional email/password authentication
- **Flexible Support**: Both Firebase tokens and JWT tokens accepted

### API Endpoints

#### Firebase Authentication
- **POST /api/v1/auth/firebase**: Authenticate with Firebase ID token (creates/updates user in both Firebase and MongoDB)
  - Accepts Firebase ID token from client-side authentication
  - Verifies token with Firebase Admin SDK
  - Creates new user in MongoDB if not exists
  - Creates missing Firebase user if needed
  - Updates existing user information from Firebase data
  - Returns JWT access token for API access

- **POST /api/v1/auth/firebase/create-user**: Create new user in both Firebase and MongoDB
  - Creates user account in Firebase Authentication
  - Creates corresponding user record in MongoDB
  - Supports email/password and OAuth users
  - Returns JWT access token for immediate API access
  - Validates email uniqueness across both systems

- **GET /api/v1/auth/firebase/config**: Get Firebase configuration for clients

#### Google OAuth
- **GET /api/v1/auth/google/callback**: Handle Google OAuth callback with state and code parameters
  - Returns: access_token, id_token, refresh_token, user data
  - Processes authorization code exchange and user creation/login

#### Firebase Email Authentication
- **Note**: Email/password authentication is handled by Firebase client-side
- **Client Side**: Use Firebase SDK for email signup, signin, password reset, email verification
- **Backend**: All Firebase authentication (Google + Email) uses the same `/auth/firebase` endpoint
- **POST /api/v1/auth/firebase**: Authenticate with Firebase ID token (supports both Google and Email providers)

#### General Authentication
- **GET /api/v1/auth/providers**: Get available authentication providers
- **GET /api/v1/auth/status**: Get authentication service status

### Authentication Flows

#### Google OAuth Flow (Direct)
1. Client redirects to Google OAuth authorization URL
2. User authorizes application on Google
3. Google redirects to `/auth/google/callback` with `code` and `state`
4. Backend exchanges authorization code for tokens (access, ID, refresh)
5. Backend verifies ID token and extracts user information
6. Backend creates/updates user in database
7. Backend returns JWT access token + Google tokens
8. Client uses JWT token for subsequent API calls

#### Firebase Authentication Flow (Google & Email)
1. **Client Side**: User signs in with Google or Email/Password using Firebase SDK
2. **Client Side**: Firebase handles authentication and returns ID token
3. **Client Side**: Send Firebase ID token to backend `/auth/firebase` endpoint
4. **Backend**: Verify token with Firebase Admin SDK
5. **Backend**: Check if user exists in MongoDB by email or Firebase UID
6. **Backend**: If user exists, update Firebase UID and profile data; if not, create new user
7. **Backend**: Set user verification status based on Firebase email verification
8. **Backend**: Update last login timestamp
9. **Backend**: Generate and return JWT access token for API access
10. **Client Side**: Use JWT token for subsequent API calls

#### Firebase Email Authentication (Client Side)
1. **Signup**: `createUserWithEmailAndPassword()` - Firebase handles user creation
2. **Signin**: `signInWithEmailAndPassword()` - Firebase handles authentication
3. **Password Reset**: `sendPasswordResetEmail()` - Firebase sends reset email
4. **Email Verification**: `sendEmailVerification()` - Firebase handles verification
5. **After any Firebase auth**: Send ID token to backend `/auth/firebase` endpoint

### User Data Synchronization
- **Automatic User Creation**: New users automatically created in MongoDB when signing in via Firebase or Google OAuth for the first time
- **Profile Data Sync**: Name, email, and avatar URL synchronized from Firebase and Google authentication providers
- **Email Verification Status**: User verification status updated based on Firebase email verification and manual verification
- **Account Linking**: Existing users linked with Firebase/Google accounts using email matching or Firebase UID
- **Username Generation**: Unique usernames generated from email addresses for OAuth users
- **Firebase UID Mapping**: Firebase user IDs stored and indexed for efficient user lookup and authentication
- **Provider Tracking**: Authentication provider information (google, email) stored for each user
- **Incremental Updates**: User profiles updated only when new information is available from authentication providers

### Configuration
- Firebase project setup required for Firebase auth
- Google OAuth credentials (CLIENT_ID, CLIENT_SECRET, REDIRECT_URI) for direct OAuth flow
- Service account credentials for Firebase Admin SDK
- Firebase Web API Key for client-side Firebase SDK initialization
- Environment variables for secure configuration
- Firebase handles all email/password authentication client-side
## Testing Strategy
- Unit tests for business logic
- Integration tests for API endpoints
- Database tests with test fixtures
- Authentication tests for protected endpoints

## Deployment Considerations
- Use Docker for containerization
- Environment-specific configuration
- Database migrations in deployment pipeline
- Health checks for load balancers
- Logging and monitoring setup

## Version History
- v1.2.0: Enhanced Firebase authentication with automatic user creation
  - Added Firebase ID token authentication endpoint (`/auth/firebase`)
  - Implemented automatic user creation/update in MongoDB for Firebase users
  - Added Firebase user synchronization with profile data (name, email, avatar, verification status)
  - Enhanced user linking by email and Firebase UID matching
  - Added Firebase UID tracking and indexing for efficient user lookup
  - Updated authentication flow documentation for Firebase user management
  - Improved error handling and logging for Firebase authentication failures

- v1.1.5: Fixed Google OAuth token exchange redirect URI configuration
  - Fixed hardcoded redirect URI in token exchange to use GOOGLE_REDIRECT_URI environment variable
  - Enhanced token exchange error logging with detailed Google API responses
  - Added validation for all required Google OAuth environment variables
  - Improved error messages to include Google's error_description
  - Added debug logging for token exchange payload (without exposing secrets)

- v1.1.4: Complete PKCE (Proof Key for Code Exchange) implementation
  - Added PKCE parameter generation endpoint (`/auth/google/pkce`)
  - Added PKCE-enabled auth URL generator (`/auth/google/auth-url-pkce`)
  - Implemented `generate_pkce()` utility function with SHA256 code challenge
  - Enhanced callback logging to show code_verifier usage
  - Added comprehensive PKCE flow documentation and examples
  - Support for both traditional OAuth and PKCE flows in same endpoint

- v1.1.3: Enhanced Google OAuth callback to handle all Google parameters
  - Added support for all Google OAuth callback parameters (scope, authuser, hd, prompt)
  - Enhanced callback logging to debug OAuth flow parameters
  - Added PKCE support with code_verifier parameter
  - Improved token exchange logging with detailed request information
  - Updated callback endpoint documentation for G Suite domains (hd parameter)

- v1.1.2: Fixed PKCE-related OAuth errors and redirect URI configuration
  - Resolved "Missing code verifier" error by documenting Web application type requirement
  - Fixed redirect URI mismatch issues (port 3000 vs 8000)
  - Added debug endpoints for OAuth troubleshooting (/debug/oauth-config, /google/auth-url)
  - Enhanced Google OAuth setup documentation with PKCE troubleshooting
  - Improved error logging to show detailed Google API responses

- v1.1.1: Fixed Google OAuth configuration and added setup documentation
  - Added missing Google OAuth environment variables (GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, GOOGLE_REDIRECT_URI)
  - Fixed external API call failures in Google OAuth callback
  - Updated configuration documentation for Google Cloud Console setup
  - Enhanced error handling and logging for OAuth token exchange
  
- v1.1.0: Enhanced authentication system with multiple providers and email features
  - Google OAuth2 callback endpoint (/auth/google/callback)
  - Email-based authentication (signup, signin, password reset, email verification)
  - Traditional email/password authentication alongside Firebase
  - Password reset workflow with secure tokens
  - Email verification system
  - Enhanced User model with password hashing and verification tokens
  - Added httpx dependency for OAuth token exchange
  - Comprehensive authentication documentation
  
- v1.0.0: Initial FastAPI setup with Firebase authentication and MongoDB backend
  - Firebase authentication with Google and Email providers
  - MongoDB database with Beanie ODM
  - JWT token generation for API access
  - Authentication-only API (no user management endpoints)
  - Automatic user creation/linking from Firebase

## Google OAuth Setup Instructions

### Prerequisites
1. Google Cloud Console project with OAuth2 credentials
2. Enabled Google+ API and Google OAuth2 API
3. Configured authorized redirect URIs

### Required Environment Variables
```bash
GOOGLE_CLIENT_ID=your-google-client-id-from-cloud-console
GOOGLE_CLIENT_SECRET=your-google-client-secret-from-cloud-console
GOOGLE_REDIRECT_URI=http://localhost:8000/api/v1/auth/google/callback
```

### Google Cloud Console Setup Steps
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create or select a project
3. Enable APIs: Google+ API, Google OAuth2 API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Set application type to "Web application"
6. Add authorized redirect URI: `http://localhost:8000/api/v1/auth/google/callback`
7. Copy the generated Client ID and Client Secret to your .env file

### Troubleshooting OAuth Errors
- **HTTP_404 "Not Found"**: Check Google OAuth credentials are properly set
- **Invalid redirect_uri**: Ensure redirect URI matches Google Cloud Console configuration
- **Invalid authorization code**: Code may have expired or been used already
- **Missing credentials**: Verify GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, and GOOGLE_REDIRECT_URI are set in .env
- **"invalid_grant" / "Bad Request"**: Most commonly caused by redirect URI mismatch
  - Ensure GOOGLE_REDIRECT_URI in .env matches what's configured in Google Cloud Console
  - Verify the redirect URI used in authorization matches the one used in token exchange
  - Check that the authorization code hasn't expired (codes expire after 10 minutes)
  - Confirm the authorization code hasn't been used already (codes are single-use)

### OAuth Flow URLs
- **Authorization URL**: `https://accounts.google.com/o/oauth2/auth`
- **Token Exchange URL**: `https://oauth2.googleapis.com/token`
- **Callback Endpoint**: `/api/v1/auth/google/callback`