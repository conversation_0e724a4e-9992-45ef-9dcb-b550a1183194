# Google OAuth Setup Guide

## Overview
This guide helps you resolve the Google OAuth callback failure and set up Google authentication for your Botmani backend.

## Error Resolution
If you're seeing this error:
```json
{
    "message": "External API call failed",
    "error": "{\"success\":false,\"error\":{\"code\":\"HTTP_404\",\"message\":\"Not Found\",\"details\":[]},\"meta\":{\"timestamp\":\"2025-07-12T18:56:25.695257Z\",\"version\":\"1.0.0\"}}"
}
```

This indicates that Google OAuth credentials are not properly configured in your environment.

## Quick Fix Steps

### 1. Check Current Configuration
Use the debug endpoint to verify your setup:
```bash
curl http://localhost:8000/api/v1/auth/debug/oauth-config
```

### 2. Update Environment Variables
Replace the placeholder values in your `.env` file:

```bash
# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-actual-google-client-id
GOOGLE_CLIENT_SECRET=your-actual-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:8000/api/v1/auth/google/callback
```

### 2. Get Google OAuth Credentials

#### Step 1: Google Cloud Console Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable required APIs:
   - Google+ API
   - Google OAuth2 API

#### Step 2: Create OAuth 2.0 Credentials
1. Navigate to **APIs & Services** → **Credentials**
2. Click **Create Credentials** → **OAuth 2.0 Client IDs**
3. **IMPORTANT**: Choose **Web application** as application type (NOT Desktop/Mobile)
4. Configure the following:
   - **Name**: Botmani Backend OAuth
   - **Authorized JavaScript origins**: `http://localhost:8000`
   - **Authorized redirect URIs**: `http://localhost:8000/api/v1/auth/google/callback`

#### Step 3: Copy Credentials
1. Copy the **Client ID** and **Client Secret**
2. Update your `.env` file with these values
3. Restart your FastAPI server

## Testing the Setup

### 1. Check OAuth Configuration
```bash
curl http://localhost:8000/api/v1/auth/providers
```

Should return:
```json
{
    "success": true,
    "data": {
        "providers": ["email", "google", "firebase_email"],
        "firebase_available": true
    }
}
```

### 2. Generate Test OAuth URL
Use the auth URL endpoint to get the correct OAuth URL:
```bash
curl "http://localhost:8000/api/v1/auth/google/auth-url?state=test123"
```

### 3. Test OAuth Flow
1. Use the generated auth URL from step 2
2. After authorization, you should be redirected to your callback endpoint
3. The callback should return user data and tokens

## Common Issues & Solutions

### Issue 1: HTTP_400 "Missing code verifier" (PKCE Error)
**Cause**: Frontend-initiated OAuth flow with public client requiring PKCE
**Solutions** (choose one):

#### Option A: Configure as Web Application (Recommended)
1. Go to [Google Cloud Console](https://console.cloud.google.com/) → APIs & Services → Credentials
2. Edit your OAuth 2.0 Client ID: `1060720916507-ri4148sndh7dpnfbr1l8j7mq1ardhpad.apps.googleusercontent.com`
3. Change **Application type** to "Web application"
4. Set authorized redirect URIs to: `http://localhost:8000/api/v1/auth/google/callback`
5. Update your frontend to use backend OAuth flow instead

#### Option B: Fix Frontend PKCE Implementation
1. Keep client as "Desktop application"
2. Set redirect URI to: `http://localhost:3000/auth/google/callback`
3. Implement PKCE in frontend (code_challenge/code_verifier)
4. Pass code_verifier to backend callback: `?code_verifier=your_verifier`

#### Option C: Dual Redirect Setup
1. Add both redirect URIs in Google Console:
   - `http://localhost:3000/auth/google/callback`
   - `http://localhost:8000/api/v1/auth/google/callback`
2. Frontend handles OAuth, then forwards to backend

### Issue 2: HTTP_400 "Failed to exchange authorization code for tokens"
**Cause**: Most commonly redirect_uri mismatch between authorization and token exchange
**Debug Steps**:
1. Check configuration: `curl http://localhost:8000/api/v1/auth/debug/oauth-config`
2. Verify redirect_uri in Google Cloud Console matches exactly
3. Check server logs for detailed error from Google API
4. Ensure authorization code is fresh (not expired or reused)

### Issue 2: "OAuth not configured"
**Cause**: Missing `GOOGLE_CLIENT_ID` or `GOOGLE_CLIENT_SECRET`
**Solution**: Set both environment variables and restart server

### Issue 3: "Invalid redirect_uri"
**Cause**: Redirect URI doesn't match Google Cloud Console configuration
**Solution**: Ensure exact match between `.env` and Google Cloud Console

### Issue 4: "Invalid authorization code"
**Cause**: Authorization code expired or already used
**Solution**: Generate new authorization code (codes are single-use)

### Issue 5: HTTP 404 from Google API
**Cause**: Incorrect client credentials or malformed request
**Solution**: Verify credentials and check request parameters

## Production Configuration

For production deployment, update these values:

```bash
# Production OAuth Configuration
GOOGLE_CLIENT_ID=your-production-client-id
GOOGLE_CLIENT_SECRET=your-production-client-secret
GOOGLE_REDIRECT_URI=https://yourdomain.com/api/v1/auth/google/callback
```

And update Google Cloud Console with production redirect URIs.

## Security Considerations

1. **Never commit credentials**: Keep `.env` file in `.gitignore`
2. **Use HTTPS in production**: OAuth requires HTTPS for production
3. **Validate state parameter**: Implement proper CSRF protection
4. **Rotate credentials**: Regularly update OAuth credentials
5. **Restrict origins**: Limit authorized JavaScript origins to your domains

## API Endpoints

### OAuth Flow Endpoints
- **OAuth Callback**: `GET|POST /api/v1/auth/google/callback`
- **Generate Auth URL**: `GET /api/v1/auth/google/auth-url?state=test123`
- **Generate PKCE Parameters**: `GET /api/v1/auth/google/pkce`
- **Generate Auth URL with PKCE**: `GET /api/v1/auth/google/auth-url-pkce`

### Debug & Status Endpoints
- **Debug OAuth Config**: `GET /api/v1/auth/debug/oauth-config`
- **Auth Providers**: `GET /api/v1/auth/providers`
- **Auth Status**: `GET /api/v1/auth/status`
- **Firebase Config**: `GET /api/v1/auth/firebase/config`

## PKCE (Proof Key for Code Exchange) Flow

### Option 1: Generate PKCE Parameters Separately
```bash
# Step 1: Generate PKCE parameters
curl http://localhost:8000/api/v1/auth/google/pkce

# Response includes:
# - code_verifier (store securely)
# - code_challenge (use in auth URL)
# - state (use in auth URL)
```

### Option 2: Generate Auth URL with PKCE
```bash
# Generate auth URL with automatic PKCE
curl http://localhost:8000/api/v1/auth/google/auth-url-pkce

# Returns complete auth URL with code_challenge
```

### Complete PKCE OAuth Flow
1. **Generate PKCE**: `GET /api/v1/auth/google/pkce`
2. **Store code_verifier** securely in your frontend
3. **Redirect user** to the generated auth URL (includes code_challenge)
4. **Handle callback** with code_verifier:
   ```
   GET /api/v1/auth/google/callback?code=...&state=...&code_verifier=YOUR_CODE_VERIFIER
   ```

## Support

If you continue experiencing issues:
1. Check server logs for detailed error messages
2. Verify all environment variables are set correctly
3. Ensure Google Cloud Console APIs are enabled
4. Test with a fresh authorization code

## Related Documentation

- [Firebase Setup Guide](./firebase_setup.md)
- [Authentication Flow Documentation](../knowledge.md#firebase-authentication)
- [Google OAuth 2.0 Documentation](https://developers.google.com/identity/protocols/oauth2)