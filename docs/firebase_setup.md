# Firebase Authentication Setup Guide

This guide explains how to set up Firebase authentication with Google and Email providers for the Botmani Backend API.

## Prerequisites

1. A Firebase project (create one at [Firebase Console](https://console.firebase.google.com/))
2. Firebase Authentication enabled in your project
3. Google and Email/Password providers enabled

## Firebase Console Setup

### 1. Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Follow the setup wizard

### 2. Enable Authentication

1. In your Firebase project, go to "Authentication" in the left sidebar
2. Click "Get started"
3. Go to "Sign-in method" tab
4. Enable the following providers:
   - **Email/Password**: Click and enable "Email/Password"
   - **Google**: Click and enable "Google", configure OAuth consent screen

### 3. Generate Service Account Key

1. Go to Project Settings (gear icon) → "Service accounts"
2. Click "Generate new private key"
3. Download the JSON file (keep it secure!)

### 4. Get Web API Key

1. Go to Project Settings (gear icon) → "General"
2. In "Your apps" section, click "Web app" or add a web app
3. Copy the "Web API Key" (this is public and safe to use in frontend)

## Backend Configuration

### 1. Environment Variables

Add the following variables to your `.env` file:

```bash
# Firebase Configuration
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/your-service-account%40your-project-id.iam.gserviceaccount.com
FIREBASE_WEB_API_KEY=your-web-api-key
```

**Note**: The `FIREBASE_PRIVATE_KEY` should be the entire private key from the service account JSON, with `\n` for line breaks.

### 2. Extract Values from Service Account JSON

From your downloaded service account JSON file, extract these values:

```json
{
  "type": "service_account",
  "project_id": "your-project-id",                    // → FIREBASE_PROJECT_ID
  "private_key_id": "your-private-key-id",            // → FIREBASE_PRIVATE_KEY_ID
  "private_key": "-----BEGIN PRIVATE KEY-----\n...",  // → FIREBASE_PRIVATE_KEY
  "client_email": "<EMAIL>...",   // → FIREBASE_CLIENT_EMAIL
  "client_id": "your-client-id",                      // → FIREBASE_CLIENT_ID
  "client_x509_cert_url": "https://www.googleapis..." // → FIREBASE_CLIENT_X509_CERT_URL
}
```

## MongoDB Setup

Ensure MongoDB is running and accessible:

```bash
# Start MongoDB (if using local installation)
mongod

# Or using Docker
docker run -d -p 27017:27017 --name mongodb mongo:latest

# The application will automatically create collections and indexes
```

## API Endpoints

### 1. Firebase Authentication

**POST** `/api/v1/auth/firebase`

Authenticate with Firebase ID token:

```json
{
  "id_token": "firebase-id-token-from-client"
}
```

Response:
```json
{
  "success": true,
  "message": "Firebase authentication successful (google)",
  "data": {
    "token": {
      "access_token": "jwt-token",
      "token_type": "bearer",
      "expires_in": 1800
    },
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "username": "user",
      "full_name": "User Name",
      "firebase_uid": "firebase-user-uid",
      "auth_provider": "google",
      "is_verified": true
    },
    "provider": "google",
    "firebase_uid": "firebase-user-uid"
  }
}
```

### 2. Firebase Configuration

**GET** `/api/v1/auth/firebase/config`

Get client-side Firebase configuration:

```json
{
  "success": true,
  "message": "Firebase configuration retrieved",
  "data": {
    "project_id": "your-project-id",
    "auth_domain": "your-project-id.firebaseapp.com",
    "api_key": "your-web-api-key",
    "providers": ["google", "email"]
  }
}
```

## Frontend Integration Example

### JavaScript/React Example

```javascript
// Install Firebase SDK
// npm install firebase

import { initializeApp } from 'firebase/app';
import { getAuth, signInWithPopup, GoogleAuthProvider, signInWithEmailAndPassword } from 'firebase/auth';

// Get Firebase config from your backend
const getFirebaseConfig = async () => {
  const response = await fetch('/api/v1/auth/firebase/config');
  const data = await response.json();
  return data.data;
};

// Initialize Firebase
const firebaseConfig = await getFirebaseConfig();
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

// Google Sign-In
const signInWithGoogle = async () => {
  const provider = new GoogleAuthProvider();
  try {
    const result = await signInWithPopup(auth, provider);
    const idToken = await result.user.getIdToken();
    
    // Send token to your backend
    const response = await fetch('/api/v1/auth/firebase', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ id_token: idToken }),
    });
    
    const authData = await response.json();
    // Store the JWT token for API calls
    localStorage.setItem('token', authData.data.token.access_token);
  } catch (error) {
    console.error('Google sign-in error:', error);
  }
};

// Email/Password Sign-In
const signInWithEmail = async (email, password) => {
  try {
    const result = await signInWithEmailAndPassword(auth, email, password);
    const idToken = await result.user.getIdToken();
    
    // Send token to your backend
    const response = await fetch('/api/v1/auth/firebase', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ id_token: idToken }),
    });
    
    const authData = await response.json();
    localStorage.setItem('token', authData.data.token.access_token);
  } catch (error) {
    console.error('Email sign-in error:', error);
  }
};
```

## Authentication Flow

1. **Client Side**: User signs in with Google or Email/Password using Firebase SDK
2. **Client Side**: Get Firebase ID token from the authentication result
3. **Client Side**: Send ID token to your backend API (`/auth/firebase`)
4. **Backend**: Verify the Firebase ID token with Firebase Admin SDK
5. **Backend**: Create or update user in your database
6. **Backend**: Generate your own JWT token for API access
7. **Backend**: Return JWT token and user data to client
8. **Client Side**: Use JWT token for subsequent API calls

## Security Considerations

1. **Private Key Security**: Never expose the Firebase private key in client code
2. **Token Validation**: Always validate Firebase tokens on the server side
3. **CORS**: Configure CORS properly for your frontend domain
4. **HTTPS**: Always use HTTPS in production
5. **Token Expiry**: Handle token expiry and refresh appropriately

## Troubleshooting

### Common Issues

1. **Invalid Firebase Configuration**: Check all environment variables are set correctly
2. **Private Key Format**: Ensure private key has proper `\n` line breaks
3. **Project ID Mismatch**: Verify project ID matches between client and server
4. **Authentication Domain**: Ensure auth domain is correctly configured

### Testing Firebase Setup

```bash
# Test if Firebase is properly configured
curl -X GET http://localhost:8000/api/v1/auth/firebase/config

# Should return Firebase configuration without errors
```

## Migration from Existing Users

Existing users with email/password authentication can be linked to Firebase accounts:

1. User signs in with existing credentials
2. In frontend, link Firebase account using `linkWithCredential()`
3. Next login can use Firebase authentication
4. Backend automatically links accounts by email

This setup provides secure, scalable authentication with Google and Email providers while maintaining your existing JWT-based API structure.