AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31

Description: Botmani Backend FastAPI application deployed to Lambda

Globals:
  Function:
    Timeout: 30
    MemorySize: 1024

Parameters:
  # Deployment Configuration
  Stage:
    Type: String
    Default: "dev"

Resources:
  BotmaniApi:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "botmani-backend-${Stage}"
      PackageType: Image
      ImageConfig:
        Command: ["app.lambda_handler.handler"]
      Environment:
        Variables:
          STAGE: !Ref Stage
      Events:
        HttpApiEvent:
          Type: HttpApi
          Properties:
            ApiId: !Ref HttpApi
            Method: ANY
            Path: /{proxy+}
        RootEvent:
          Type: HttpApi
          Properties:
            ApiId: !Ref HttpApi
            Method: ANY
            Path: /
    Metadata:
      Dockerfile: Dockerfile.lambda
      DockerContext: .
      DockerTag: python3.11-v1
      DockerBuildArgs:
        STAGE: !Ref Stage

  HttpApi:
    Type: AWS::Serverless::HttpApi
    Properties:
      Name: !Sub "botmani-backend-api-${Stage}"
      Description: Botmani Backend API
      CorsConfiguration:
        AllowOrigins:
          - "http://localhost:3000"
          - "http://localhost:3001"
          - "https://dev.botmani.com"
          - "https://staging.botmani.com"
          - "https://botmani.com"
        AllowHeaders:
          - "Content-Type"
          - "Authorization"
          - "X-Requested-With"
        AllowMethods:
          - "GET"
          - "POST"
          - "PUT"
          - "DELETE"
          - "OPTIONS"
        AllowCredentials: true

Outputs:
  ApiUrl:
    Description: "HTTP API Gateway URL"
    Value: !GetAtt HttpApi.ApiEndpoint
    Export:
      Name: !Sub "${AWS::StackName}-ApiUrl"
  
  FunctionName:
    Description: "Lambda Function Name"
    Value: !Ref BotmaniApi
    Export:
      Name: !Sub "${AWS::StackName}-FunctionName"