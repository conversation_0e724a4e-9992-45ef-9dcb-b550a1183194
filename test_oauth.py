#!/usr/bin/env python3
"""
Test script for Google OAuth callback endpoint.
"""

import asyncio
import httpx
import json

async def test_oauth_endpoint():
    """Test the OAuth callback endpoint with debug info."""
    
    base_url = "http://localhost:8000"
    
    print("🔍 Testing OAuth endpoint and debug info...")
    
    async with httpx.AsyncClient() as client:
        # Test health endpoint
        print("\n1. Testing health endpoint...")
        try:
            response = await client.get(f"{base_url}/api/v1/health")
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print(f"   Response: {response.json()}")
            else:
                print(f"   Error: {response.text}")
        except Exception as e:
            print(f"   Error: {str(e)}")
        
        # Test debug config
        print("\n2. Testing debug config...")
        try:
            response = await client.get(f"{base_url}/api/v1/debug/config")
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                config = response.json()
                print(f"   Stage: {config['data']['stage']}")
                print(f"   MongoDB URL set: {bool(config['data'].get('mongodb_url'))}")
                print(f"   Google Client ID set: {config['data']['google_client_id_set']}")
            else:
                print(f"   Error: {response.text}")
        except Exception as e:
            print(f"   Error: {str(e)}")
        
        # Test MongoDB debug
        print("\n3. Testing MongoDB connection...")
        try:
            response = await client.get(f"{base_url}/api/v1/debug/mongodb")
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                mongodb_info = response.json()
                print(f"   Connected: {mongodb_info['data']['connected']}")
                if mongodb_info['data']['connected']:
                    print(f"   Database: {mongodb_info['data']['database_name']}")
                    print(f"   Collections: {mongodb_info['data']['collections']}")
                else:
                    print(f"   Error: {mongodb_info['data'].get('error', 'Unknown error')}")
            else:
                print(f"   Error: {response.text}")
        except Exception as e:
            print(f"   Error: {str(e)}")
        
        # Test environment debug
        print("\n4. Testing environment info...")
        try:
            response = await client.get(f"{base_url}/api/v1/debug/environment")
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                env_info = response.json()
                print(f"   Stage: {env_info['data']['stage']}")
                print(f"   Lambda Task Root: {env_info['data']['lambda_task_root']}")
                print(f"   Working Directory: {env_info['data']['current_working_directory']}")
            else:
                print(f"   Error: {response.text}")
        except Exception as e:
            print(f"   Error: {str(e)}")

if __name__ == "__main__":
    print("🚀 Starting OAuth endpoint test...")
    print("Make sure your SAM local API is running on http://localhost:8000")
    print("Run: ./sam_commands.sh run")
    print()
    
    try:
        asyncio.run(test_oauth_endpoint())
        print("\n✅ Test completed!")
    except KeyboardInterrupt:
        print("\n❌ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}") 