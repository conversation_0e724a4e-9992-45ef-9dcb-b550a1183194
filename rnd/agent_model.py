from pydantic import BaseModel, <PERSON>, validator, SecretStr
from typing import List, Optional, Dict, Any, Union
from enum import Enum
from datetime import datetime
import uuid

# Based on <PERSON>gno's existing provider patterns
class ProviderType(str, Enum):
    """Enumeration of supported AI providers - aligned with <PERSON><PERSON>'s architecture"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    AZURE = "azure"
    HUGGING_FACE = "huggingface"
    COHERE = "cohere"
    MISTRAL = "mistral"
    OLLAMA = "ollama"
    GROQ = "groq"
    PERPLEXITY = "perplexity"
    CEREBRAS = "cerebras"
    LITELLM = "litellm"


class ModelType(str, Enum):
    """Enumeration of model types - aligned with <PERSON><PERSON>'s patterns"""
    CHAT = "chat"
    COMPLETION = "completion"
    EMBEDDING = "embedding"
    IMAGE_GENERATION = "image_generation"
    TEXT_TO_SPEECH = "text_to_speech"
    SPEECH_TO_TEXT = "speech_to_text"
    REASONING = "reasoning"  # For models like o1


class ToolType(str, Enum):
    """Enumeration of tool types - based on <PERSON><PERSON>'s tool ecosystem"""
    FUNCTION = "function"
    API = "api"
    DATABASE = "database"
    WEB_SCRAPER = "web_scraper"
    FILE_PROCESSOR = "file_processor"
    CALCULATOR = "calculator"
    CODE_EXECUTOR = "code_executor"
    APIFY = "apify"  # Agno has Apify integration
    BRAVE_SEARCH = "brave_search"
    FIRECRAWL = "firecrawl"
    GITHUB = "github"
    GMAIL = "gmail"
    GOOGLE_MAPS = "google_maps"


class AgentStatus(str, Enum):
    """Enumeration of agent statuses - aligned with Agno's run status patterns"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    TRAINING = "training"
    MAINTENANCE = "maintenance"
    DEPRECATED = "deprecated"
    PAUSED = "paused"
    ERROR = "error"


class Provider(BaseModel):
    """Model representing an AI provider - based on Agno's provider architecture"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique provider identifier")
    name: str = Field(..., min_length=1, max_length=100, description="Provider name")
    type: ProviderType = Field(..., description="Type of provider")
    base_url: Optional[str] = Field(None, description="Base API URL for the provider")
    description: Optional[str] = Field(None, max_length=500, description="Provider description")
    is_active: bool = Field(default=True, description="Whether the provider is active")
    rate_limits: Optional[Dict[str, int]] = Field(default=None, description="Rate limits configuration")
    supported_model_types: List[ModelType] = Field(default_factory=list, description="Supported model types")
    # Agno-specific fields
    extra_headers: Optional[Dict[str, str]] = Field(default=None, description="Extra headers for API requests")
    request_params: Optional[Dict[str, Any]] = Field(default=None, description="Default request parameters")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)

    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ApiKey(BaseModel):
    """Model representing API keys for providers"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique API key identifier")
    name: str = Field(..., min_length=1, max_length=100, description="Human-readable name for the API key")
    provider_id: str = Field(..., description="Associated provider ID")
    key_value: SecretStr = Field(..., description="The actual API key (encrypted/hidden)")
    is_active: bool = Field(default=True, description="Whether the API key is active")
    usage_limit: Optional[int] = Field(None, ge=0, description="Usage limit for this key")
    current_usage: int = Field(default=0, ge=0, description="Current usage count")
    expires_at: Optional[datetime] = Field(None, description="When the API key expires")
    scopes: List[str] = Field(default_factory=list, description="Permissions/scopes for this key")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_used_at: Optional[datetime] = Field(default=None)

    @classmethod
    def validate_usage_against_limit(cls, v, values):
        usage_limit = values.data.get('usage_limit') if hasattr(values, 'data') else values.get('usage_limit')
        if usage_limit is not None and v > usage_limit:
            raise ValueError('Current usage cannot exceed usage limit')
        return v

    from pydantic import field_validator
    validate_usage_against_limit = field_validator('current_usage', mode='before')(validate_usage_against_limit)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class Model(BaseModel):
    """Model representing an AI model - based on Agno's Model base class patterns"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique model identifier")
    name: str = Field(..., min_length=1, max_length=100, description="Model name")
    provider_id: str = Field(..., description="Associated provider ID")
    model_type: ModelType = Field(..., description="Type of model")
    version: Optional[str] = Field(None, description="Model version")
    # Agno-specific model parameters
    max_tokens: Optional[int] = Field(None, gt=0, description="Maximum tokens supported")
    max_completion_tokens: Optional[int] = Field(None, gt=0, description="Max completion tokens")
    context_window: Optional[int] = Field(None, gt=0, description="Context window size")
    input_cost_per_token: Optional[float] = Field(None, ge=0, description="Cost per input token")
    output_cost_per_token: Optional[float] = Field(None, ge=0, description="Cost per output token")
    supports_streaming: bool = Field(default=False, description="Whether model supports streaming")
    supports_function_calling: bool = Field(default=False, description="Whether model supports function calling")
    supports_structured_outputs: bool = Field(default=False, description="Whether model supports structured outputs")
    supports_json_schema: bool = Field(default=False, description="Whether model supports JSON schema")
    # Temperature and sampling parameters (like Agno's models)
    temperature: Optional[float] = Field(default=None, ge=0.0, le=2.0, description="Default temperature")
    top_p: Optional[float] = Field(default=None, ge=0.0, le=1.0, description="Default top-p")
    frequency_penalty: Optional[float] = Field(default=None, ge=-2.0, le=2.0, description="Default frequency penalty")
    presence_penalty: Optional[float] = Field(default=None, ge=-2.0, le=2.0, description="Default presence penalty")
    reasoning_effort: Optional[str] = Field(default=None, description="Reasoning effort for o1 models")
    capabilities: List[str] = Field(default_factory=list, description="Model capabilities")
    parameters: Optional[Dict[str, Any]] = Field(default=None, description="Model-specific parameters")
    is_active: bool = Field(default=True, description="Whether the model is active")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)

    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        protected_namespaces = ()


class ToolParameter(BaseModel):
    """Model representing a tool parameter"""
    name: str = Field(..., min_length=1, description="Parameter name")
    type: str = Field(..., description="Parameter type (e.g., 'string', 'integer', 'boolean')")
    description: Optional[str] = Field(None, description="Parameter description")
    required: bool = Field(default=False, description="Whether parameter is required")
    default_value: Optional[Any] = Field(None, description="Default value for parameter")
    enum_values: Optional[List[Any]] = Field(None, description="Allowed values if parameter is enum")
    validation_pattern: Optional[str] = Field(None, description="Regex pattern for validation")


class Tool(BaseModel):
    """Model representing a tool that agents can use - based on Agno's Function/Tool patterns"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique tool identifier")
    name: str = Field(..., min_length=1, max_length=100, description="Tool name")
    type: ToolType = Field(..., description="Type of tool")
    description: str = Field(..., min_length=1, max_length=1000, description="Tool description")
    version: str = Field(default="1.0.0", description="Tool version")
    endpoint: Optional[str] = Field(None, description="API endpoint if applicable")
    authentication_required: bool = Field(default=False, description="Whether tool requires authentication")
    parameters: List[ToolParameter] = Field(default_factory=list, description="Tool parameters")
    return_type: Optional[str] = Field(None, description="Expected return type")
    timeout: Optional[int] = Field(default=30, gt=0, description="Timeout in seconds")
    rate_limit: Optional[int] = Field(None, gt=0, description="Rate limit per minute")
    # Agno-specific tool fields
    function_schema: Optional[Dict[str, Any]] = Field(default=None, description="OpenAI function schema")
    sanitize_args: bool = Field(default=True, description="Whether to sanitize function arguments")
    show_arguments: bool = Field(default=True, description="Whether to show function arguments to the model")
    agent_id: Optional[str] = Field(None, description="Associated agent ID if tool is agent-specific")
    tags: List[str] = Field(default_factory=list, description="Tool tags for categorization")
    is_active: bool = Field(default=True, description="Whether the tool is active")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)

    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class AgentConfig(BaseModel):
    """Configuration settings for an agent - based on Agno's Agent patterns"""
    # Model parameters (aligned with Agno's model configuration)
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="Sampling temperature")
    max_tokens: Optional[int] = Field(default=None, gt=0, description="Maximum tokens to generate")
    max_completion_tokens: Optional[int] = Field(default=None, gt=0, description="Maximum completion tokens")
    top_p: Optional[float] = Field(default=None, ge=0.0, le=1.0, description="Top-p sampling parameter")
    frequency_penalty: Optional[float] = Field(default=None, ge=-2.0, le=2.0, description="Frequency penalty")
    presence_penalty: Optional[float] = Field(default=None, ge=-2.0, le=2.0, description="Presence penalty")
    stop_sequences: List[str] = Field(default_factory=list, description="Stop sequences")
    # Agno-specific agent configuration
    reasoning_effort: Optional[str] = Field(default=None, description="Reasoning effort for o1 models")
    tool_choice: Optional[Union[str, Dict[str, Any]]] = Field(default=None, description="Tool choice strategy")
    tool_call_limit: Optional[int] = Field(default=None, gt=0, description="Maximum tool calls per turn")
    show_tool_calls: bool = Field(default=True, description="Whether to show tool calls")
    markdown: bool = Field(default=False, description="Whether to use markdown formatting")
    # Memory and session configuration
    add_messages_to_memory: bool = Field(default=True, description="Whether to add messages to memory")
    add_tool_execution_results_to_memory: bool = Field(default=True, description="Add tool results to memory")
    num_messages_to_add_to_model: Optional[int] = Field(default=None, description="Number of messages to add to model")
    # Timing and retry configuration
    timeout: int = Field(default=60, gt=0, description="Request timeout in seconds")
    retry_attempts: int = Field(default=3, ge=0, description="Number of retry attempts")
    # Custom parameters
    custom_parameters: Optional[Dict[str, Any]] = Field(default=None, description="Custom parameters")


class Agent(BaseModel):
    """Model representing an AI agent - based on Agno's Agent class architecture"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique agent identifier")
    name: str = Field(..., min_length=1, max_length=100, description="Agent name")
    description: Optional[str] = Field(None, max_length=1000, description="Agent description")
    # Core agent configuration (like Agno's Agent)
    system_prompt: Optional[str] = Field(None, description="System prompt for the agent")
    instructions: List[str] = Field(default_factory=list, description="Additional instructions")
    model_id: str = Field(..., description="Primary model ID to use")
    fallback_model_ids: List[str] = Field(default_factory=list, description="Fallback model IDs")
    tool_ids: List[str] = Field(default_factory=list, description="Available tool IDs")
    config: AgentConfig = Field(default_factory=AgentConfig, description="Agent configuration")
    # Agno-specific agent fields
    session_id: Optional[str] = Field(None, description="Current session ID")
    user_id: Optional[str] = Field(None, description="Associated user ID")
    team_id: Optional[str] = Field(None, description="Associated team ID")
    app_id: Optional[str] = Field(None, description="Associated app ID")
    workflow_id: Optional[str] = Field(None, description="Associated workflow ID")
    # Agent state and metadata
    status: AgentStatus = Field(default=AgentStatus.ACTIVE, description="Agent status")
    tags: List[str] = Field(default_factory=list, description="Agent tags for categorization")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Additional metadata")
    usage_stats: Optional[Dict[str, int]] = Field(default_factory=dict, description="Usage statistics")
    is_public: bool = Field(default=False, description="Whether agent is publicly available")
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
    last_used_at: Optional[datetime] = Field(default=None)

    @classmethod
    def validate_fallback_models(cls, v, values):
        model_id = values.data.get('model_id') if hasattr(values, 'data') else values.get('model_id')
        if model_id and model_id in v:
            raise ValueError('Primary model cannot be in fallback models list')
        return v

    from pydantic import field_validator
    validate_fallback_models = field_validator('fallback_model_ids', mode='before')(validate_fallback_models)

    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        protected_namespaces = ()


# Example usage and helper functions
class AgentSystem(BaseModel):
    """Complete agent system with all components"""
    providers: List[Provider] = Field(default_factory=list)
    api_keys: List[ApiKey] = Field(default_factory=list)
    models: List[Model] = Field(default_factory=list)
    tools: List[Tool] = Field(default_factory=list)
    agents: List[Agent] = Field(default_factory=list)

    def get_provider_by_id(self, provider_id: str) -> Optional[Provider]:
        return next((p for p in self.providers if p.id == provider_id), None)

    def get_model_by_id(self, model_id: str) -> Optional[Model]:
        return next((m for m in self.models if m.id == model_id), None)

    def get_tool_by_id(self, tool_id: str) -> Optional[Tool]:
        return next((t for t in self.tools if t.id == tool_id), None)

    def get_agent_by_id(self, agent_id: str) -> Optional[Agent]:
        return next((a for a in self.agents if a.id == agent_id), None)

    def get_active_agents(self) -> List[Agent]:
        return [a for a in self.agents if a.status == AgentStatus.ACTIVE]


# Example instantiation - Updated to match Agno's patterns
if __name__ == "__main__":
    # Create a sample provider (OpenAI)
    openai_provider = Provider(
        name="OpenAI",
        type=ProviderType.OPENAI,
        base_url="https://api.openai.com/v1",
        description="OpenAI API provider",
        supported_model_types=[ModelType.CHAT, ModelType.COMPLETION, ModelType.EMBEDDING],
        extra_headers={"User-Agent": "agno/1.0"},
        request_params={"timeout": 60}
    )

    # Create a sample API key
    api_key = ApiKey(
        name="OpenAI Production Key",
        provider_id=openai_provider.id,
        key_value=SecretStr("sk-..."),
        usage_limit=1000000,
        scopes=["chat", "completion"]
    )

    # Create a sample model (GPT-4o with Agno-style configuration)
    gpt4o_model = Model(
        name="GPT-4o",
        provider_id=openai_provider.id,
        model_type=ModelType.CHAT,
        version="gpt-4o-2024-11-20",
        max_tokens=4096,
        max_completion_tokens=16384,
        context_window=128000,
        supports_streaming=True,
        supports_function_calling=True,
        supports_structured_outputs=True,
        supports_json_schema=True,
        temperature=0.7,
        capabilities=["text", "vision", "audio"]
    )

    # Create a sample tool (Brave Search - common in Agno)
    brave_search_tool = Tool(
        name="Brave Search",
        type=ToolType.BRAVE_SEARCH,
        description="Search the web using Brave Search API",
        parameters=[
            ToolParameter(
                name="query",
                type="string",
                description="Search query",
                required=True
            ),
            ToolParameter(
                name="count",
                type="integer",
                description="Number of results to return",
                default_value=10
            )
        ],
        function_schema={
            "type": "function",
            "function": {
                "name": "brave_search",
                "description": "Search the web using Brave Search API",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Search query"},
                        "count": {"type": "integer", "description": "Number of results"}
                    },
                    "required": ["query"]
                }
            }
        },
        tags=["web", "search"]
    )

    # Create a sample agent (with Agno-style configuration)
    research_agent = Agent(
        name="Research Assistant",
        description="An AI agent that helps with research tasks using web search",
        system_prompt="You are a helpful research assistant that can search the web for information.",
        instructions=["Always cite your sources", "Provide accurate and up-to-date information"],
        model_id=gpt4o_model.id,
        tool_ids=[brave_search_tool.id],
        config=AgentConfig(
            temperature=0.3,
            max_tokens=2048,
            show_tool_calls=True,
            markdown=True,
            tool_call_limit=5
        ),
        tags=["research", "assistant", "web-search"]
    )

    print("Sample Agno-aligned models created successfully!")
    print(f"Agent: {research_agent.name} (ID: {research_agent.id})")
    print(f"Model: {gpt4o_model.name} (ID: {gpt4o_model.id})")
    print(f"Provider: {openai_provider.name} (ID: {openai_provider.id})")
    print(f"Tool: {brave_search_tool.name} (ID: {brave_search_tool.id})")

    # Create an AgentSystem to demonstrate relationships
    agent_system = AgentSystem(
        providers=[openai_provider],
        api_keys=[api_key],
        models=[gpt4o_model],
        tools=[brave_search_tool],
        agents=[research_agent]
    )

    # Demonstrate helper methods
    print("\n=== Agno-Aligned Agent System Demo ===")
    print(f"Active agents: {len(agent_system.get_active_agents())}")

    # Show agent configuration
    agent = agent_system.get_agent_by_id(research_agent.id)
    if agent:
        print(f"Agent '{agent.name}' uses model: {agent.model_id}")
        print(f"Available tools: {agent.tool_ids}")
        print(f"Temperature: {agent.config.temperature}")
        print(f"Supports tool calls: {agent.config.show_tool_calls}")

    # Show model capabilities
    model = agent_system.get_model_by_id(gpt4o_model.id)
    if model:
        print(f"Model '{model.name}' supports:")
        print(f"  - Streaming: {model.supports_streaming}")
        print(f"  - Function calling: {model.supports_function_calling}")
        print(f"  - Structured outputs: {model.supports_structured_outputs}")
        print(f"  - Context window: {model.context_window:,} tokens")

    print("\n=== Integration with Agno Framework ===")
    print("These models are designed to work with:")
    print("- Agno's existing Agent class architecture")
    print("- Provider-specific model implementations (OpenAI, Anthropic, etc.)")
    print("- Tool/Function system with automatic schema generation")
    print("- Memory and session management")
    print("- Event-driven run responses and streaming")